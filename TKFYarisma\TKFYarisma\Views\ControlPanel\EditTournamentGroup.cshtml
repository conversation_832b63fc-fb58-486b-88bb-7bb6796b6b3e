﻿@using TKFYarisma.Helpers

@model TKFYarisma.Models.TournamentGroupModel

@{
    ViewBag.Title = "Grup Düzenle";
    ViewBag.Section = "Tournaments";
}

<div class="row">
    <div class="col">
        <div class="tile-header tile-header-big">
            <div class="row">
                <div class="col mr-auto">
                    <h3><PERSON><PERSON><PERSON> Düzenle (@Model.Tournament.Name)</h3>
                </div>
            </div>
        </div>
        <div class="tile">
            @using (Html.BeginForm("EditTournamentGroup", "ControlPanel", FormMethod.Post))
            {
                @Html.HiddenFor(d => d.TournamentGroup.Id)
                @Html.HiddenFor(d => d.TournamentGroup.TournamentId)

                <div class="row">
                    <div class="col">
                        <div class="row row-form">
                            <div class="col-form-label-medium">
                                <span class="align-middle">Grup Adı</span>
                            </div>
                            <div class="col">
                                @Html.TextBoxFor(d => d.TournamentGroup.Name, new { @class = "form-control", autofocus = "autofocus" })
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col">
                        <div class="row mt-3">
                            <div class="col">
                                <div class="row row-form-footer">
                                    <input class="btn btn-primary" type="submit" value="Kaydet" />
                                    <a href="javascript: history.back();" class="btn btn-secondary ml-2">İptal</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>