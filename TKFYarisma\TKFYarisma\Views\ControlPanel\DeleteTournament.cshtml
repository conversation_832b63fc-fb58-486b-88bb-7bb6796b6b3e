﻿@using TKFYarisma.Helpers

@model TKFYarisma.Models.Tournament

@{
    ViewBag.Title = "Yarışma Silme";
    ViewBag.Section = "Tournaments";
}

<div class="row">
    <div class="col">
        <div class="tile-header tile-header-big">
            <div class="row">
                <div class="col mr-auto">
                    <h3>Yarış<PERSON> Silme</h3>
                </div>
            </div>
        </div>
        <div class="tile">
            @using (Html.BeginForm("DeleteTournament", "ControlPanel", FormMethod.Post))
            {
                <div class="row">
                    <div class="col">
                        <div class="row row-form">
                            <div class="col-form-label-medium">
                                <span class="align-middle">Yarışma Adı</span>
                            </div>
                            <div class="col">
                                @Html.TextBoxFor(d => d.Name, new { @class = "form-control", disabled = "disabled" })
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col">
                        <div class="row row-form-footer">
                            <input class="btn btn-danger" type="submit" value="Sil" />
                            <a href="javascript: history.back();" class="btn btn-secondary ml-2">İptal</a>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>