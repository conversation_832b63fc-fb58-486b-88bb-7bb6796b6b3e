#athlete_scores #background {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
}

#athlete_scores #tournament {
    position: absolute;
    left: 500px;
    top: 140px;
    width: 1420px;
    height: 60px;
}

    #athlete_scores #tournament #background {
        position: absolute;
        right: 0px;
        top: 0px;
        width: auto;
        height: auto;
    }

    #athlete_scores #tournament #tournament_name_and_phase {
        color: #f0f0f0;
        position: absolute;
        padding-right: 0px;
        right: 70px;
        top: 10px;
        width: 1250px;
        height: 350px;
        font-size: 30pt;
        text-align: right;
        line-height: 30pt;
        letter-spacing: 2px;
        text-shadow: 0px 0px 5px #800000;
    }

#athlete_scores #list {
    position: absolute;
    left: 0px;
    top: 220px;
    right: 0px;
    bottom: 0px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
}

    #athlete_scores #list #title-container {
        width: 1600px;
        height: 50px;
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
    }

    #athlete_scores #list #score_1_title {
        font-family: "Exo-BoldItalic";
        font-size: 30pt;
        width: 225px;
        text-align: center;
    }

    #athlete_scores #list #score_2_title {
        font-family: "Exo-BoldItalic";
        font-size: 30pt;
        text-align: center;
        width: 225px;
    }

    #athlete_scores #list #score_3_title {
        font-family: "Exo-BoldItalic";
        font-size: 30pt;
        text-align: center;
        width: 225px;
    }


    #athlete_scores #list #row-prototype {
        display: none;
    }

    #athlete_scores #list #row_container {
        position: absolute;
        left: 0px;
        top: 70px;
        right: 0px;
        bottom: 0px;
        overflow: hidden;
    }

        #athlete_scores #list #row_container #scroller {
            position: absolute;
            left: 0px;
            top: 0px;
            right: 0px;
            bottom: 0px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            padding-top: 20px;
        }

#athlete_scores #row {
    position: relative;
    width: 1600px;
    height: 130px;
    min-height: 130px;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
}

    #athlete_scores #row #background {
        position: absolute;
        left: -300px;
        top: 0px;
        width: 2520px;
        height: 110px;
        z-index: -1;
    }

    #athlete_scores #row #container {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-content: center;
        width: 1600px;
        height: 120px;
    }

#row #container #athlete_order {
    position: relative;
    top: -10px;
    width: 160px;
    height: 120px;
}

#row #container #athlete_order_background {
    position: absolute;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 123px;
}

#row #container #athlete_order_text {
    position: absolute;
    left: calc(50% - 100px);
    top: calc(50% - 60px);
    width: 180px;
    height: 100px;
    text-align: center;
    font-family: "Exo-BlackItalic";
    font-size: 70pt;
}

#row #container #athlete_and_city_name_holder {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    flex: 1;
    height: 110px;
    margin-left: 50px;
}

    #athlete_scores #list #row #container #athlete_and_city_name_holder #athlete_name {
        color: #f0f0f0;
        width: auto;
        height: auto;
        letter-spacing: 2px;
        font-family: "Anton";
        font-size: 60pt;
        line-height: 60pt;
        text-shadow: 0px 0px 5px #800000;
        max-width: 700px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }

    #athlete_scores #list #row #container #athlete_and_city_name_holder #athlete_city_name {
        color: #f0f0f0;
        width: auto;
        height: auto;
        font-size: 30pt;
        line-height: 30pt;
        letter-spacing: 2px;
        text-shadow: 0px 0px 5px #800000;
        padding-bottom: 3px;
        margin-left: 15px;
        display: none;
    }

#athlete_scores #list #row #container #score_1 {
    position: relative;
    width: 225px;
    height: 123px;
}

    #athlete_scores #list #row #container #score_1 #background {
        position: absolute;
        left: 0px;
        top: -10px;
        width: 225px;
        height: 123px;
    }

    #athlete_scores #list #row #container #score_1 #value {
        font-family: "Exo-BoldItalic";
        font-size: 50pt;
        position: absolute;
        left: 0px;
        top: calc(50% - 70px);
        width: 225px;
        height: 123px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

#athlete_scores #list #row #container #score_2 {
    position: relative;
    width: 225px;
    height: 123px;
}

    #athlete_scores #list #row #container #score_2 #background {
        position: absolute;
        left: 0px;
        top: -10px;
        width: 225px;
        height: 123px;
    }

    #athlete_scores #list #row #container #score_2 #value {
        font-family: "Exo-BoldItalic";
        font-size: 50pt;
        position: absolute;
        left: 0px;
        top: calc(50% - 70px);
        width: 225px;
        height: 123px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

#athlete_scores #list #row #container #score_3 {
    position: relative;
    width: 225px;
    height: 123px;
}

    #athlete_scores #list #row #container #score_3 #background {
        position: absolute;
        left: 0px;
        top: -10px;
        width: 225px;
        height: 123px;
    }

    #athlete_scores #list #row #container #score_3 #value {
        font-family: "Exo-BoldItalic";
        font-size: 50pt;
        position: absolute;
        left: 0px;
        top: calc(50% - 70px);
        width: 225px;
        height: 123px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

#athlete_scores #list #row #container #queue {
    font-family: "FranklinGothic-MediumItalic";
    font-size: 50pt;
    color: white;
    text-shadow: 3px 3px 2px #800000;
    position: absolute;
    left: 1620px;
    top: 10px;
    width: 225px;
    height: 123px;
    display: none;
}

#athlete_scores #athlete #background {
    position: absolute;
    left: 0px;
    top: 140px;
    width: auto;
    height: auto;
}

#athlete_scores #photo {
    position: absolute;
    left: 200px;
    top: 185px;
    width: auto;
    height: auto;
    -webkit-mask-image: url(../../img/photo_mask.png);
    mask-image: url(../img/photo_mask.png);
    -webkit-mask-position: center center;
    mask-position: center center;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
}

#athlete_scores #athlete #name {
    color: #f0f0f0;
    position: absolute;
    left: 600px;
    top: 190px;
    width: 1050px;
    height: auto;
    font-size: 80pt;
    line-height: 80pt;
    text-shadow: 2px 2px 5px #800000;
}

#athlete_scores #athlete #district_and_city {
    color: #f0f0f0;
    position: absolute;
    left: 600px;
    top: 310px;
    width: 1050px;
    height: auto;
    font-size: 40pt;
    line-height: 40pt;
    text-shadow: 2px 2px 5px #800000;
}
