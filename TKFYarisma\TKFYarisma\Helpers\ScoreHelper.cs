﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace TKFYarisma.Helpers
{
    public class ScoreHelper
    {
        public static double GetHighestScore(double score1, double score2, double score3)
        {
            if (score1 >= score2 && score1 >= score3)
                return score1;
            else if (score2 >= score1 && score2 >= score3)
                return score2;
            else if (score3 >= score1 && score3 >= score2)
                return score3;
            else
                return 0;
        }

        public static double GetBestTrickSummary(List<double> scores, int? count)
        {
            double result = 0;

            int count_ = 3;
            if (count != null)
                count_ = count.Value;

            for (int i = 0; i < count_; i++)
                result += scores[scores.Count - i - 1];

            return result;
        }
    }
}