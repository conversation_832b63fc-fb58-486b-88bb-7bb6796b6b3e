function ViewStateAthleteTimerController() {
    var self = this;
    var totalTime = 0;
    var intervalId = undefined;

    this.viewStateName = "athlete_timer";

    this.getDigitValue = function (value, digitCount) {
        var text = value.toString();
        var count = text.length;
        if (count < digitCount) {
            for(var i = 0; i< digitCount - count; i++) {
                text = "0" + text; 
            }
        }
        return text;
    }

    this.refreshTimer = function (time) {
        var minutes = Math.floor(time / 60);
        var seconds = time % 60;

        var minutesStr = this.getDigitValue(minutes, 2);
        var secondsStr = this.getDigitValue(seconds, 2);

        ViewStateHelper.setProperty(this.viewStateName, "timer_digit_1", minutesStr[0]);
        ViewStateHelper.setProperty(this.viewStateName, "timer_digit_2", minutesStr[1]);
        ViewStateHelper.setProperty(this.viewStateName, "timer_digit_3", secondsStr[0]);
        ViewStateHelper.setProperty(this.viewStateName, "timer_digit_4", secondsStr[1]);
    }

    this.initialize = function (properties) {
        var $viewstate = ElementHelper.getElementById($("body"), self.viewStateName);

        self.totalTime = ViewStateHelper.getProperty(properties, "total_time");

        var timerStarted = ViewStateHelper.getProperty(properties, "timer_started");
        var timerFinished = ViewStateHelper.getProperty(properties, "timer_finished");

        var endPhase = ViewStateHelper.getProperty(properties, "end_phase");

        var previousScore = ViewStateHelper.getProperty(properties, "previous_score");
        var $previousScore = $(ElementHelper.getElementById($viewstate, "previous_score"));
        if (previousScore == null || endPhase)
            $previousScore.css("display", "none");
        else
            $previousScore.css("display", "block");

        // under age
        var $helmet = $(ElementHelper.getElementById($viewstate, "helmet"));
        var underAge = ViewStateHelper.getProperty(properties, "under_age");
        if (underAge)
            $helmet.css("display", "block");
        else
            $helmet.css("display", "none");

        if (!timerFinished)
            this.refreshTimer(self.totalTime);
        else
            this.refreshTimer(0);

        if (timerStarted && !timerFinished)
            self.intervalId = setInterval(this.onInterval, 1000);
    }

    this.onInterval = function () {
        if (self.totalTime > 0) {
            self.totalTime--;
            self.refreshTimer(self.totalTime);
        } else {
            clearInterval(self.intervalId);
            self.intervalId = undefined;

            var SERVER_URL = Preferences.SERVER_TIME_FINISHED_URL;
            var SERVER_TIMEOUT = Preferences.SERVER_TIME_FINISHED_TIMEOUT;

            $.ajax({
                url: SERVER_URL,
                timeout: SERVER_TIMEOUT,
                async: true,
                type: "POST",
                processData: false,
                success: function (result) {

                },
                error: function (request, status, error) {
                    console.info("timer error");
                }
            });
        }
    }

    this.deinitialize = function () {
        if (self.intervalId != undefined)
            clearInterval(self.intervalId);
    }
}