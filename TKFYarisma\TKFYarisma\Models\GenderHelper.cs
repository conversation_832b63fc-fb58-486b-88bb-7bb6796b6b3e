﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace TKFYarisma.Models
{
    public class GenderHelper
    {
        public static Gender FromInt(int? value)
        {
            if (value != null && value.HasValue)
            {
                switch (value)
                {
                    case 1:
                        return Gender.Male;
                    case 2:
                        return Gender.Female;
                    case 0:
                    default:
                        return Gender.None;
                }
            }
            else
                return Gender.None;
        }

        public static int ToInt(Gender gender)
        {
            switch (gender)
            {
                case Gender.Male:
                    return 1;
                case Gender.Female:
                    return 2;
                default:
                case Gender.None:
                    return 0;
            }
        }
    }
}