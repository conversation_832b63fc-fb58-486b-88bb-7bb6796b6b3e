﻿@using TKFYarisma.Helpers

@model TKFYarisma.Models.TournamentGroupsModel

@{
    ViewBag.Title = "Yarışma Grupları";
    ViewBag.Section = "Tournaments";
}

<div class="row">
    <div class="col">
        <div class="tile-header tile-header-big">
            <div class="row">
                <div class="col mr-auto">
                    <h3>Yarışma Grupları</h3>
                </div>
                <div class="col-auto text-right">
                    <a href="@Url.Action("CreateTournamentGroup", new { id = Model.Tournament.Id })" class="btn btn-dark"><i class="icon fas fa-plus"></i>Grup Oluştur</a>
                </div>
            </div>
        </div>
        <div class="tile">
            <div class="row">
                <div class="col">
                    @if (Model.TournamentGroups.Count() > 0)
                    {
                        var grid = new WebGrid(Model.TournamentGroups,
                                                canPage: true,
                                                rowsPerPage: 10,
                                                selectionFieldName: "selectedRow",
                                                ajaxUpdateContainerId: "gridContent");

                        var gridColumns = new List<WebGridColumn>();

                        gridColumns.Add(grid.Column("Name", "Grup Adı", style: "table-icon-column w-100"));
                        gridColumns.Add(grid.Column("Id", " ", format: (item) => { return Html.IconLink("TournamentGroupReferees", new { id = item.Id }, IconType.Referees, IconSize.Large, IconColor.Black, "Hakemler"); }, style: "table-icon-column"));
                        gridColumns.Add(grid.Column("Id", " ", format: (item) => { return Html.IconLink("TournamentGroupAthletes", new { id = item.Id }, IconType.Athletes, IconSize.Large, IconColor.Black, "Sporcular"); }, style: "table-icon-column"));
                        gridColumns.Add(grid.Column("Id", " ", format: (item) => { return Html.IconLink("TournamentGroupTeams", new { id = item.Id }, IconType.Teams, IconSize.Large, IconColor.Black, "Takımlar"); }, style: "table-icon-column"));
                        gridColumns.Add(grid.Column("Id", " ", format: (item) => { return Html.IconLink("EditTournamentGroup", new { id = item.Id }, IconType.Edit, IconSize.Large, IconColor.Black, "Düzenle"); }, style: "table-icon-column"));
                        gridColumns.Add(grid.Column("Id", " ", format: (item) => { return Html.IconLink("DeleteTournamentGroup", new { id = item.Id }, IconType.Delete, IconSize.Large, IconColor.Black, "Sil"); }, style: "table-icon-column"));

                        grid.Pager(WebGridPagerModes.All);

                        <div id="gridContent">
                            @grid.GetHtml(
                                        tableStyle: "webgrid table table-hover table-bordered",
                                        headerStyle: "sorting_asc",
                                        footerStyle: "table-footer",
                                        rowStyle: "even",
                                        alternatingRowStyle: "odd",
                                        selectedRowStyle: "webgrid-selected-row",
                                        columns: gridColumns)
                        </div>
                    }
                    else
                    {
                        <div class="no-record">
                            There is no tournament group record yet.
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
