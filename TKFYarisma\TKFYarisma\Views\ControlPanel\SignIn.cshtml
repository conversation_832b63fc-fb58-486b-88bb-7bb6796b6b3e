﻿@using TKFYarisma.Models

@{
    Layout = null;
}

@model TKFYarisma.Models.SignInModel

<!doctype html>
<html>
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    @Styles.Render("~/Content/css")
    @Styles.Render("~/Content/site")
    @Scripts.Render("~/bundles/modernizr")
    <link href="https://fonts.googleapis.com/css?family=Open+Sans" rel="stylesheet">

    <title>Türkiye Kaykay Federasyonu - Kullanıcı Girişi</title>
</head>
<body class="sign-in">
    @using (Html.BeginForm("SignIn", "ControlPanel", FormMethod.Post, new { id = "formSignIn" }))
    {
        <div class="container sign-in-container">
            <div class="row">
                <div class="col">
                    <img class="img-fluid" src="@Url.Content("~/Content/Site/images/logo.png")" alt="logo" />
                </div>
            </div>
            <div class="row mt-2">
                <div class="col text-center">
                    <h3>Kullanıcı Girişi</h3>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col text-center sign-in-error-text">
                    @{
                        if (Model.ErrorType == SignInErrorType.UserNotExist)
                        {
                            <text>Kullanıcı mevcut değil</text>
                        }
                        else if (Model.ErrorType == SignInErrorType.UserDisabled)
                        {
                            <text> Kullanıcı hesabı engellenmiş</text>
                        }
                        else if (Model.ErrorType == SignInErrorType.PasswordIncorrect)
                        {
                            <text> Şifre geçersiz</text>
                        }
                    }
                </div>
            </div>
            <div class="row mt-2">
                <div class="col">
                    @Html.TextBoxFor(m => m.Username, new { @class = "form-control", placeholder = "Kullanıcı adınız" })
                </div>
            </div>
            <div class="row mt-2">
                <div class="col">
                    @Html.PasswordFor(m => m.Password, new { @class = "form-control", placeholder = "Şifreniz" })
                </div>
            </div>
            <div class="row mt-3">
                <div class="col text-center">
                    @Html.CheckBoxFor(m => m.RememberMe, new { @class = "checkbox" })
                    <label for="RememberMe">Beni Hatırla</label>
                </div>
            </div>
            <div class="row mb-2">
                <div class="col text-center">
                    <input class="btn btn-primary" type="submit" value="Giriş" />
                    <a href="@Url.Action("ForgotPassword")" lang="tr" class="btn btn-secondary">Şifremi Unuttum</a>
                </div>
            </div>
            <div class="row mt-2">
                <div class="copyright col mt-3 text-center">
                    Sebit Eğitim ve Bilgi Teknolojileri AŞ &copy; Her hakkı saklıdır.
                </div>
            </div>
        </div>
    }

    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/bootstrap")
</body>
</html>