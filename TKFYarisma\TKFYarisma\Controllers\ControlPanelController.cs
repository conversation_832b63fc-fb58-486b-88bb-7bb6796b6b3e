﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using TKFYarisma.Filters;
using TKFYarisma.Models;
using TKFYarisma.Helpers;
using System.IO;
using System.Text;
using OfficeOpenXml;

namespace TKFYarisma.Controllers
{
    public class ControlPanelController : Controller
    {
        [Security]
        public ActionResult Index()
        {
            return RedirectToAction("ControlCenter");
        }

        public ActionResult SignIn()
        {
            return View(new SignInModel());
        }

        [HttpPost]
        public ActionResult SignIn(SignInModel model)
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                var user = (from table in dbContext.Users
                            where table.Username == model.Username
                            select table).SingleOrDefault();
                if (user != null)
                {
                    if (user.Enabled == true)
                    {
                        if (user.Password == HashHelper.SHA1(model.Password))
                        {
                            model.ErrorType = SignInErrorType.None;

                            Session["user"] = new SessionUser()
                            {
                                Id = user.Id,
                                FirstName = user.FirstName,
                                LastName = user.LastName,
                            };

                            return RedirectToAction("Index");
                        }
                        else
                            model.ErrorType = SignInErrorType.PasswordIncorrect;
                    }
                    else
                        model.ErrorType = SignInErrorType.UserDisabled;
                }
                else
                    model.ErrorType = SignInErrorType.UserNotExist;

                return View(model);
            }
        }

        public ActionResult SignOut()
        {
            Session["user"] = null;

            return RedirectToAction("SignIn");
        }

        public ActionResult ControlCenter()
        {
            var model = new ControlCenterModel();

            using (var dbContext = new TKFYarismaEntities())
            {
                var controlCenter = (from table in dbContext.ControlCenters select table).FirstOrDefault();
                model.Tournaments = (from table in dbContext.Tournaments select table).ToList();
                model.Tournament = (from table in dbContext.Tournaments where table.Id == controlCenter.TournamentId select table).SingleOrDefault();
                model.TournamentDays = (from table in dbContext.TournamentDays where table.TournamentId == model.Tournament.Id select table).ToList();
            }

            return View(model);
        }

        public ActionResult Tournaments()
        {
            ViewBag.Path = PathHelper.Tournaments();

            using (var dbContext = new TKFYarismaEntities())
            {
                var tournaments = (from table in dbContext.Tournaments
                                   select table).ToList();

                return View(tournaments);
            }
        }

        public ActionResult CreateTournament()
        {
            ViewBag.Path = PathHelper.CreateTournament();

            return View(new Tournament());
        }

        [HttpPost]
        public ActionResult CreateTournament(Tournament tournament)
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                dbContext.Tournaments.Add(tournament);
                dbContext.SaveChanges();
            }

            return RedirectToAction("Tournaments");
        }

        public ActionResult EditTournament(int? id)
        {
            ViewBag.Path = PathHelper.EditTournament(id);

            using (var dbContext = new TKFYarismaEntities())
            {
                var tournament = (from table in dbContext.Tournaments
                                  where table.Id == id
                                  select table).SingleOrDefault();

                return View(tournament);
            }
        }

        [HttpPost]
        public ActionResult EditTournament(int? id, Tournament tournament)
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                dbContext.Tournaments.Attach(tournament);
                dbContext.Entry<Tournament>(tournament).State = System.Data.Entity.EntityState.Modified;
                dbContext.SaveChanges();
            }

            return RedirectToAction("Tournaments");
        }

        public ActionResult DeleteTournament(int? id)
        {
            ViewBag.Path = PathHelper.DeleteTournament(id);

            using (var dbContext = new TKFYarismaEntities())
            {
                var tournament = (from table in dbContext.Tournaments
                                  where table.Id == id
                                  select table).SingleOrDefault();

                return View(tournament);
            }
        }

        [HttpPost]
        public ActionResult DeleteTournament(int? id, Tournament tournament)
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                dbContext.Tournaments.Attach(tournament);
                dbContext.Entry<Tournament>(tournament).State = System.Data.Entity.EntityState.Deleted;
                dbContext.SaveChanges();
            }

            return RedirectToAction("Tournaments");
        }

        public ActionResult TournamentGroups(int? id)
        {
            ViewBag.Path = PathHelper.TournamentGroups(id);

            var model = new TournamentGroupsModel();

            using (var dbContext = new TKFYarismaEntities())
            {
                model.Tournament = (from table in dbContext.Tournaments
                                    where table.Id == id
                                    select table).SingleOrDefault();

                model.TournamentGroups = (from table in dbContext.TournamentGroups
                                          where table.TournamentId == id
                                          select table).ToList();

                return View(model);
            }
        }

        public ActionResult CreateTournamentGroup(int? id)
        {
            ViewBag.Path = PathHelper.CreateTournamentGroup(id);

            var model = new TournamentGroupModel();

            using (var dbContext = new TKFYarismaEntities())
            {
                model.Tournament = (from table in dbContext.Tournaments
                                    where table.Id == id
                                    select table).SingleOrDefault();

                model.TournamentGroup = new TournamentGroup()
                {
                    TournamentId = id
                };
            }

            return View(model);
        }

        [HttpPost]
        public ActionResult CreateTournamentGroup(int? id, TournamentGroupModel model)
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                dbContext.TournamentGroups.Add(model.TournamentGroup);
                dbContext.SaveChanges();
            }

            return RedirectToAction("TournamentGroups", new { id = model.TournamentGroup.TournamentId });
        }


        public ActionResult EditTournamentGroup(int? id)
        {
            ViewBag.Path = PathHelper.EditTournamentGroup(id);

            var model = new TournamentGroupModel();

            using (var dbContext = new TKFYarismaEntities())
            {
                model.TournamentGroup = (from table in dbContext.TournamentGroups
                                       where table.Id == id
                                       select table).SingleOrDefault();

                model.Tournament = (from table in dbContext.Tournaments
                                    where table.Id == model.TournamentGroup.TournamentId
                                    select table).SingleOrDefault();

                return View(model);
            }
        }

        [HttpPost]
        public ActionResult EditTournamentGroup(int? id, TournamentGroupModel model)
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                dbContext.TournamentGroups.Attach(model.TournamentGroup);
                dbContext.Entry<TournamentGroup>(model.TournamentGroup).State = System.Data.Entity.EntityState.Modified;
                dbContext.SaveChanges();
            }

            return RedirectToAction("TournamentGroups", new { id = model.TournamentGroup.TournamentId });
        }

        public ActionResult DeleteTournamentGroup(int? id)
        {
            ViewBag.Path = PathHelper.DeleteTournamentGroup(id);

            var model = new TournamentGroupModel();

            using (var dbContext = new TKFYarismaEntities())
            {
                model.TournamentGroup = (from table in dbContext.TournamentGroups
                                         where table.Id == id
                                         select table).SingleOrDefault();

                model.Tournament = (from table in dbContext.Tournaments
                                    where table.Id == model.TournamentGroup.TournamentId
                                    select table).SingleOrDefault();

                return View(model);
            }
        }

        [HttpPost]
        public ActionResult DeleteTournamentGroup(int? id, TournamentGroupModel model)
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                dbContext.TournamentGroups.Attach(model.TournamentGroup);
                dbContext.Entry<TournamentGroup>(model.TournamentGroup).State = System.Data.Entity.EntityState.Deleted;
                dbContext.SaveChanges();
            }

            return RedirectToAction("TournamentGroups", new { id = model.TournamentGroup.TournamentId });
        }


        public ActionResult TournamentGroupReferees(int? id)
        {
            var model = new TournamentGroupModel();

            using (var dbContext = new TKFYarismaEntities())
            {
                model.TournamentGroup = (from table in dbContext.TournamentGroups
                                         where table.Id == id
                                         select table).SingleOrDefault();

                model.Tournament = (from table in dbContext.Tournaments
                                    where table.Id == model.TournamentGroup.TournamentId
                                    select table).SingleOrDefault();

            }

            return View(model);
        }

        [HttpPost]
        public ActionResult TournamentGroupReferees(int? id, TournamentGroupModel model)
        {
            
            return RedirectToAction("TournamentGroups", new { id = model.TournamentGroup.TournamentId });
        }

        public ActionResult TournamentGroupAthletes(int? id)
        {
            ViewBag.Path = PathHelper.TournamentGroupAthletes(id);

            var model = new TournamentGroupAthletesModel();

            using (var dbContext = new TKFYarismaEntities())
            {
                model.TournamentGroup = (from table in dbContext.TournamentGroups
                                         where table.Id == id
                                         select table).SingleOrDefault();

                model.Tournament = (from table in dbContext.Tournaments
                                    where table.Id == model.TournamentGroup.TournamentId
                                    select table).SingleOrDefault();

                model.TournamentGroupAthletes = (from table in dbContext.ViewTournamentGroupAthletes
                                                 where table.GroupId == id
                                                 select table).ToList();
                return View(model);
            }

        }

        public ActionResult AddTournamentGroupAthletes(int? id)
        {
            return View();
        }


        public ActionResult Athletes()
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                var athletes = (from table in dbContext.ViewAthletes
                                orderby table.FirstName, table.LastName
                                select table).ToList();

                return View(athletes);
            }
        }

        public ActionResult CreateAthlete()
        {
            var model = new AthleteModel();

            using (var dbContext = new TKFYarismaEntities())
            {
                model.Districts = (from table in dbContext.Districts
                                   orderby table.Name
                                   select table).ToList();

                model.DistrictCities = (from table in dbContext.DistrictCities
                                        orderby table.Name
                                        select table).ToList();

                model.Athlete = new Athlete();
            }

            return View(model);
        }

        [HttpPost]
        public ActionResult CreateAthlete(AthleteModel model, HttpPostedFileBase file)
        {
            if (file != null)
            {
                var fileExtension = Path.GetExtension(file.FileName);
                var fileName = Path.GetFileNameWithoutExtension(Path.GetRandomFileName()) + fileExtension;
                var filePath = System.IO.Path.Combine(Server.MapPath("~/Uploads/Athlete/Photo"), fileName);

                file.SaveAs(filePath);

                model.Athlete.PhotoFilename = fileName;
            }

            using (var dbContext = new TKFYarismaEntities())
            {
                dbContext.Athletes.Add(model.Athlete);
                dbContext.SaveChanges();
            }

            return RedirectToAction("Athletes");
        }

        public ActionResult ImportAthletes()
        {
            return View();
        }

        [HttpPost]
        public ActionResult ImportAthletes(AthleteModel model, HttpPostedFileBase file)
        {
            if (file != null && file.ContentLength > 0)
            {
                var fileExtension = Path.GetExtension(file.FileName);
                var fileName = Path.GetFileNameWithoutExtension(Path.GetRandomFileName()) + fileExtension;
                var filePath = System.IO.Path.Combine(Server.MapPath("~/Uploads/Athlete/Import"), fileName);

                using (var package = new ExcelPackage(file.InputStream))
                {
                    var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                    if (worksheet != null)
                    {
                        using (var dbContext = new TKFYarismaEntities())
                        {
                            var rowCount = worksheet.Dimension.End.Row;
                            for (int rowIndex = 1; rowIndex <= rowCount; rowIndex++)
                            {
                                var firstName = worksheet.Cells[rowIndex, 1].Value != null ? Convert.ToString(worksheet.Cells[rowIndex, 1].Value) : string.Empty;
                                var lastName = worksheet.Cells[rowIndex, 2].Value != null ? Convert.ToString(worksheet.Cells[rowIndex, 2].Value) : string.Empty;
                                var gender = worksheet.Cells[rowIndex, 3].Value != null ? Convert.ToInt32(worksheet.Cells[rowIndex, 3].Value) : 0;
                                var districtId = worksheet.Cells[rowIndex, 5].Value != null ? Convert.ToInt32(worksheet.Cells[rowIndex, 5].Value) : 0;
                                var cityId = worksheet.Cells[rowIndex, 6].Value != null ? Convert.ToInt32(worksheet.Cells[rowIndex, 6].Value) : 0;

                                dbContext.Athletes.Add(new Athlete()
                                {
                                    FirstName = firstName,
                                    LastName = lastName,
                                    Gender = gender,
                                    BirthDate = DateTime.Now,
                                    DistrictId = districtId,
                                    CityId = (from table in dbContext.DistrictCities where table.PlateNumber == cityId select table).SingleOrDefault().Id
                                });
                            }

                            dbContext.SaveChanges();
                        }
                    }
                }

                return RedirectToAction("Athletes");
            }
            else
                return View();
        }

        public ActionResult EditAthlete(int? id = 1)
        {
            var model = new AthleteModel();

            using (var dbContext = new TKFYarismaEntities())
            {
                model.Districts = (from table in dbContext.Districts
                                   orderby table.Name
                                   select table).ToList();

                model.DistrictCities = (from table in dbContext.DistrictCities
                                        orderby table.Name
                                        select table).ToList();

                model.Athlete = (from table in dbContext.Athletes
                                 where table.Id == id
                                 select table).SingleOrDefault();
            }

            return View(model);
        }

        [HttpPost]
        public ActionResult EditAthlete(AthleteModel model, HttpPostedFileBase file)
        {
            if (file != null)
            {
                var fileExtension = Path.GetExtension(file.FileName);
                var fileName = Path.GetFileNameWithoutExtension(Path.GetRandomFileName()) + fileExtension;
                var filePath = System.IO.Path.Combine(Server.MapPath("~/Uploads/Athlete/Photo"), fileName);

                file.SaveAs(filePath);

                model.Athlete.PhotoFilename = fileName;
            }

            using (var dbContext = new TKFYarismaEntities())
            {
                dbContext.Athletes.Attach(model.Athlete);
                dbContext.Entry<Athlete>(model.Athlete).State = System.Data.Entity.EntityState.Modified;
                dbContext.SaveChanges();
            }

            return RedirectToAction("Athletes");
        }

        public ActionResult DeleteAthlete(int? id = 1)
        {
            var model = new AthleteModel();

            using (var dbContext = new TKFYarismaEntities())
            {
                model.Districts = (from table in dbContext.Districts
                                   orderby table.Name
                                   select table).ToList();

                model.DistrictCities = (from table in dbContext.DistrictCities
                                        orderby table.Name
                                        select table).ToList();

                model.Athlete = (from table in dbContext.Athletes
                                 where table.Id == id
                                 select table).SingleOrDefault();
            }

            return View(model);
        }

        [HttpPost]
        public ActionResult DeleteAthlete(AthleteModel model)
        {
            if (!string.IsNullOrEmpty(model.Athlete.PhotoFilename))
            {
                var fileName = Path.Combine(Server.MapPath("~/Uploads/Athlete/Photo"), model.Athlete.PhotoFilename);
                if (System.IO.File.Exists(fileName))
                    System.IO.File.Delete(fileName);
            }

            using (var dbContext = new TKFYarismaEntities())
            {
                dbContext.Athletes.Attach(model.Athlete);
                dbContext.Entry<Athlete>(model.Athlete).State = System.Data.Entity.EntityState.Deleted;
                dbContext.SaveChanges();
            }

            return RedirectToAction("Athletes");
        }

        public ActionResult Referees()
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                var referees = (from table in dbContext.ViewReferees
                                orderby table.FirstName, table.LastName
                                select table).ToList();

                return View(referees);
            }
        }

        public ActionResult CreateReferee()
        {
            var model = new RefereeModel();

            using (var dbContext = new TKFYarismaEntities())
            {
                model.Districts = (from table in dbContext.Districts
                                   orderby table.Name
                                   select table).ToList();

                model.DistrictCities = (from table in dbContext.DistrictCities
                                        orderby table.Name
                                        select table).ToList();

                model.Referee = new Referee();
            }

            return View(model);
        }

        [HttpPost]
        public ActionResult CreateReferee(RefereeModel model, HttpPostedFileBase file)
        {
            if (file != null)
            {
                var fileExtension = Path.GetExtension(file.FileName);
                var fileName = Path.GetFileNameWithoutExtension(Path.GetRandomFileName()) + fileExtension;
                var filePath = System.IO.Path.Combine(Server.MapPath("~/Uploads/Referee/Photo"), fileName);

                file.SaveAs(filePath);

                model.Referee.PhotoFilename = fileName;
            }

            using (var dbContext = new TKFYarismaEntities())
            {
                dbContext.Referees.Add(model.Referee);
                dbContext.SaveChanges();
            }

            return RedirectToAction("Referees");
        }

        public ActionResult EditReferee(int? id = 1)
        {
            var model = new RefereeModel();

            using (var dbContext = new TKFYarismaEntities())
            {
                model.Districts = (from table in dbContext.Districts
                                   orderby table.Name
                                   select table).ToList();

                model.DistrictCities = (from table in dbContext.DistrictCities
                                        orderby table.Name
                                        select table).ToList();

                model.Referee = (from table in dbContext.Referees
                                 where table.Id == id
                                 select table).SingleOrDefault();
            }

            return View(model);
        }

        [HttpPost]
        public ActionResult EditReferee(RefereeModel model, HttpPostedFileBase file)
        {
            if (file != null)
            {
                var fileExtension = Path.GetExtension(file.FileName);
                var fileName = Path.GetFileNameWithoutExtension(Path.GetRandomFileName()) + fileExtension;
                var filePath = System.IO.Path.Combine(Server.MapPath("~/Uploads/Referee/Photo"), fileName);

                file.SaveAs(filePath);

                model.Referee.PhotoFilename = fileName;
            }

            using (var dbContext = new TKFYarismaEntities())
            {
                dbContext.Referees.Attach(model.Referee);
                dbContext.Entry<Referee>(model.Referee).State = System.Data.Entity.EntityState.Modified;
                dbContext.SaveChanges();
            }

            return RedirectToAction("Referees");
        }

        public ActionResult DeleteReferee(int? id = 1)
        {
            var model = new RefereeModel();

            using (var dbContext = new TKFYarismaEntities())
            {
                model.Districts = (from table in dbContext.Districts
                                   orderby table.Name
                                   select table).ToList();

                model.DistrictCities = (from table in dbContext.DistrictCities
                                        orderby table.Name
                                        select table).ToList();

                model.Referee = (from table in dbContext.Referees
                                 where table.Id == id
                                 select table).SingleOrDefault();
            }

            return View(model);
        }

        [HttpPost]
        public ActionResult DeleteReferee(RefereeModel model)
        {
            if (!string.IsNullOrEmpty(model.Referee.PhotoFilename))
            {
                var fileName = Path.Combine(Server.MapPath("~/Uploads/Referee/Photo"), model.Referee.PhotoFilename);
                if (System.IO.File.Exists(fileName))
                    System.IO.File.Delete(fileName);
            }

            using (var dbContext = new TKFYarismaEntities())
            {
                dbContext.Referees.Attach(model.Referee);
                dbContext.Entry<Referee>(model.Referee).State = System.Data.Entity.EntityState.Deleted;
                dbContext.SaveChanges();
            }

            return RedirectToAction("Referees");
        }
    }
}