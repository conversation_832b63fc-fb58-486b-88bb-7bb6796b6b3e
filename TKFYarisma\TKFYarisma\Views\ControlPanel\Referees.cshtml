﻿@using TKFYarisma.Helpers

@model IEnumerable<TKFYarisma.Models.ViewReferee>

@{
    ViewBag.Title = "Hakemler";
    ViewBag.Section = "Referees";
}

<div class="row">
    <div class="col">
        <div class="tile-header tile-header-big">
            <div class="row">
                <div class="col mr-auto">
                    <h3><PERSON><PERSON><PERSON></h3>
                </div>
                <div class="col-auto text-right">
                    <a href="@Url.Action("CreateReferee")" class="btn btn-dark"><i class="icon fas fa-plus"></i>Hakem Oluştur</a>
                </div>
            </div>
        </div>
        <div class="tile">
            <div class="row">
                <div class="col">
                    @if (Model.Count() > 0)
                    {
                        var grid = new WebGrid(Model,
                                                canPage: true,
                                                rowsPerPage: 5,
                                                selectionFieldName: "selectedRow",
                                                ajaxUpdateContainerId: "gridContent");

                        var gridColumns = new List<WebGridColumn>();

                        gridColumns.Add(grid.Column("LicenseNumber", "Lisans No.", style: "table-icon-column"));
                        gridColumns.Add(grid.Column("FirstName", "Adı", style: "table-icon-column"));
                        gridColumns.Add(grid.Column("LastName", "Soyadı", style: "table-icon-column"));
                        gridColumns.Add(grid.Column("Gender", header: "Cinsiyet", format: (item) =>
                        {
                            if (item.Gender != null)
                            {
                                if (item.Gender == 1)
                                    return Html.Raw("Erkek");
                                else if (item.Gender == 2)
                                    return Html.Raw("Kadın");
                                else
                                    return Html.Raw("---");
                            }
                            else
                                return Html.Raw("---");
                        }, style: "table-column"));
                        gridColumns.Add(grid.Column("DistrictName", "Bölge", style: "table-icon-column"));
                        gridColumns.Add(grid.Column("CityName", "Şehir", style: "table-icon-column"));
                        gridColumns.Add(grid.Column("Id", " ", format: (item) => { return Html.IconLink("EditReferee", new { id = item.Id }, IconType.Edit, IconSize.Large, IconColor.Black, "Düzenle"); }, style: "table-icon-column"));
                        gridColumns.Add(grid.Column("Id", " ", format: (item) => { return Html.IconLink("DeleteReferee", new { id = item.Id }, IconType.Delete, IconSize.Large, IconColor.Black, "Sil"); }, style: "table-icon-column"));

                        grid.Pager(WebGridPagerModes.All);

                        <div id="gridContent">
                            @grid.GetHtml(
                                        tableStyle: "table table-hover table-bordered",
                                        headerStyle: "sorting_asc",
                                        footerStyle: "table-footer",
                                        rowStyle: "even",
                                        alternatingRowStyle: "odd",
                                        selectedRowStyle: "webgrid-selected-row",
                                        columns: gridColumns)
                        </div>
                    }
                    else
                    {
                        <div class="no-record">
                            Henüz hiçbir hakem kaydı bulunmamaktadır.
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
