﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;

namespace TKFYarisma.Filters
{
    public class SecurityAttribute : ActionFilterAttribute
    {
        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            base.OnActionExecuting(filterContext);

            if (filterContext.HttpContext.Session["user"] == null)
                filterContext.Result = new RedirectToRouteResult(new RouteValueDictionary(new { controller = "ControlPanel", action = "SignIn" }));
        }
    }

}