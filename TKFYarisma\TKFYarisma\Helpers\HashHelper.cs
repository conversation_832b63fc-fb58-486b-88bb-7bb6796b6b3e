﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Web;

namespace TKFYarisma.Helpers
{
    public class HashHelper
    {
        public static string SHA1(string text)
        {
            var hash = (new SHA1Managed()).ComputeHash(Encoding.UTF8.GetBytes(text));
            return string.Join("", hash.Select(b => b.ToString("x2")).ToArray());
        }
    }
}