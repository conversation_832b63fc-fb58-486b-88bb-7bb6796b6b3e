﻿@using TKFYarisma.Helpers

@model TKFYarisma.Models.ControlCenterModel

@{
    ViewBag.Title = "Kontrol Merkezi";
    ViewBag.Section = "ControlCenter";
}

<div class="row">
    <div class="col">
        <div class="tile-header tile-header-big">
            <div class="row">
                <div class="col mr-auto">
                    <div id="reglament-toolbar">
                        <div class="row">
                            <div class="col-auto">
                                <!-- day -->
                                <div class="dropdown">
                                    <button class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        
                                    </button>
                                    <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton">
                                        @{
                                            var dayIndex = 1;
                                        }
                                        @foreach (var tournamentDay in Model.TournamentDays)
                                        {
                                            <a class="dropdown-item" href="#">@dayIndex<text>. Gün (</text>@string.Format("{0:dd.MM.yyyy}", tournamentDay.Date)<text>)</text></a>
                                            dayIndex++;
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="col-auto">
                                <!-- tour -->
                                <div class="dropdown">
                                    <button class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        1. Tur
                                    </button>
                                    <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton">
                                        <a class="dropdown-item" href="#">1. Tur</a>
                                        <a class="dropdown-item" href="#">2. Tur</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-auto">
                    
                </div>
            </div>
        </div>
        <div class="tile">
            <div class="row">
                <div class="col">


                </div>
            </div>
        </div>
    </div>
</div>

