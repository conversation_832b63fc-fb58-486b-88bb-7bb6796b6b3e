function ViewStateAthleteScoreController() {
    var self = this;

    this.viewStateName = "athlete_score";

    this.getDigitValue = function (value, digitCount) {
        var text = value.toString();
        var count = text.length;
        if (count < digitCount) {
            for (var i = 0; i < digitCount - count; i++) {
                text = "0" + text;
            }
        }
        return text;
    }

    this.formatScore = function (score, tournamentStyle) {
        var scoreStr = score.toString();
        var dotIndex = scoreStr.indexOf(".");
        if (dotIndex != -1) {
            var scoreHighStr = scoreStr.substring(0, dotIndex);

            if (tournamentStyle == 2) {
                for (var i = 0; i < 2 - scoreHighStr.length; i++)
                    scoreHighStr = "0" + scoreHighStr;
            }

            var scoreLowStr = scoreStr.substring(dotIndex + 1, scoreStr.length);
            if (scoreLowStr.length < 2) {
                for (var i = 0; i < 2 - scoreLowStr.length; i++)
                    scoreLowStr = scoreLowStr + "0";
            }
            if (scoreLowStr.length > 2)
                scoreLowStr = scoreLowStr.substring(0, 2);
        } else {
            var scoreHighStr = scoreStr;
            if (tournamentStyle == 2) {
                for (var i = 0; i < 2 - scoreHighStr.length; i++)
                    scoreHighStr = "0" + scoreHighStr;
            }

            var scoreLowStr = "00";
        }

        return scoreHighStr + "." + scoreLowStr;
    }

    this.initialize = function (properties) {
        var $viewState = ElementHelper.getElementById($("body"), self.viewStateName);

        var tournamentTour = ViewStateHelper.getProperty(properties, "tournament_tour");
        var tournamentStyle = ViewStateHelper.getProperty(properties, "tournament_style");
        var tournamePhaseTourCount = ViewStateHelper.getProperty(properties, "phase_tour_count");
        var bestTrick = ViewStateHelper.getProperty(properties, "tournament_best_trick");
        var bestTrickPointCount = ViewStateHelper.getProperty(properties, "tournament_best_trick_point_count");

        var score1 = ViewStateHelper.getProperty(properties, "score_1");
        var score2 = ViewStateHelper.getProperty(properties, "score_2");
        var score3 = ViewStateHelper.getProperty(properties, "score_3");
        var bestTrick1 = ViewStateHelper.getProperty(properties, "best_trick_1");
        var bestTrick2 = ViewStateHelper.getProperty(properties, "best_trick_2");
        var bestTrick3 = ViewStateHelper.getProperty(properties, "best_trick_3");
        var bestTrick4 = ViewStateHelper.getProperty(properties, "best_trick_4");
        var bestTrick5 = ViewStateHelper.getProperty(properties, "best_trick_5");
        var totalScore = ViewStateHelper.getProperty(properties, "total_score");

        var endPhase = ViewStateHelper.getProperty(properties, "end_phase");

        console.info("best trick > " + bestTrick);
        if ((!endPhase || !bestTrick)) {
            // references of the dom elements
            var $score1 = $(ElementHelper.getElementById($viewState, "score_1"));
            var $score2 = $(ElementHelper.getElementById($viewState, "score_2"));
            var $score3 = $(ElementHelper.getElementById($viewState, "score_3"));
            var $score1Text = $(ElementHelper.getElementById($score1, "text"));
            var $score2Text = $(ElementHelper.getElementById($score2, "text"));
            var $score3Text = $(ElementHelper.getElementById($score3, "text"));
            var $score1Value = $(ElementHelper.getElementById($score1, "value"));
            var $score2Value = $(ElementHelper.getElementById($score2, "value"));
            var $score3Value = $(ElementHelper.getElementById($score3, "value"));
            var $score1Background = $(ElementHelper.getElementById($score1, "background"));
            var $score2Background = $(ElementHelper.getElementById($score2, "background"));
            var $score3Background = $(ElementHelper.getElementById($score3, "background"));

            // score locating for tour1 or tour2
            var tour = ViewStateHelper.getProperty(properties, "tour");
            if (tour == 1) {
                $score2.css("display", "none");
                $score3.css("display", "none");
            } else if (tour == 2) {
                $score2.css("display", "block");
                $score3.css("display", "none");
            } else if (tour == 3) {
                $score2.css("display", "block");
                $score3.css("display", "block");
            }

            var yOffset = 560;
            if (tour == 2)
                yOffset += 170;
            else if (tour == 1)
                yOffset += 340;
            $score1.css("top", yOffset);
            $score2.css("top", yOffset + 170);
            $score3.css("top", yOffset + 340);

            // format scores
            $score1Value.html(self.formatScore(score1, tournamentStyle));
            $score2Value.html(self.formatScore(score2, tournamentStyle));
            $score3Value.html(self.formatScore(score3, tournamentStyle));

            // score highlighting
            var highestScore = 0;
            if (score1 > score2 && score1 > score3)
                highestScore = 1;
            if (score2 > score1 && score2 > score3)
                highestScore = 2;
            if (score3 > score1 && score3 > score2)
                highestScore = 3;

            if (highestScore == 1) {
                $score1Background.attr("src", "../Content/Billboard/img/viewstate/viewstate_athlete_score/athlete_score_background_highlighted.png");
                $score1Text.css("color", "#f1f1f1");
                $score1Value.css("color", "#f1f1f1");
            } else {
                $score1Background.attr("src", "../Content/Billboard/img/viewstate/viewstate_athlete_score/athlete_score_background.png");
                $score1Text.css("color", "#3a3a3a");
                $score1Value.css("color", "#3a3a3a");
            }

            if (highestScore == 2) {
                $score2Background.attr("src", "../Content/Billboard/img/viewstate/viewstate_athlete_score/athlete_score_background_highlighted.png");
                $score2Text.css("color", "#f1f1f1");
                $score2Value.css("color", "#f1f1f1");
            } else {
                $score2Background.attr("src", "../Content/Billboard/img/viewstate/viewstate_athlete_score/athlete_score_background.png");
                $score2Text.css("color", "#3a3a3a");
                $score2Value.css("color", "#3a3a3a");
            }

            if (highestScore == 3) {
                $score3Background.attr("src", "../Content/Billboard/img/viewstate/viewstate_athlete_score/athlete_score_background_highlighted.png");
                $score3Text.css("color", "#f1f1f1");
                $score3Value.css("color", "#f1f1f1");
            } else {
                $score3Background.attr("src", "../Content/Billboard/img/viewstate/viewstate_athlete_score/athlete_score_background.png");
                $score3Text.css("color", "#3a3a3a");
                $score3Value.css("color", "#3a3a3a");
            }

            $score2Text.html("2.Tur");
            $score2Text.css("font-size", "50pt");
            $score2Text.css("top", "50px");
            $score2Text.css("left", "50px");
            $score2Text.css("line-height", "none");

            $score3Text.html("3.Tur");
            $score3Text.css("font-size", "50pt");
            $score3Text.css("top", "50px");
            $score3Text.css("left", "50px");
            $score3Text.css("line-height", "none");

            var $score1b = $(ElementHelper.getElementById($viewState, "score_1b"));
            $score1b.css("display", "none");
            var $score2b = $(ElementHelper.getElementById($viewState, "score_2b"));
            $score2b.css("display", "none");
            var $score3b = $(ElementHelper.getElementById($viewState, "score_3b"));
            $score3b.css("display", "none");

            var $scoreBestTrick1 = $(ElementHelper.getElementById($viewState, "score_best_trick_1"));
            $scoreBestTrick1.css("display", "none");
            var $scoreBestTrick2 = $(ElementHelper.getElementById($viewState, "score_best_trick_2"));
            $scoreBestTrick2.css("display", "none");
            var $scoreBestTrick3 = $(ElementHelper.getElementById($viewState, "score_best_trick_3"));
            $scoreBestTrick3.css("display", "none");
            var $scoreBestTrick4 = $(ElementHelper.getElementById($viewState, "score_best_trick_4"));
            $scoreBestTrick4.css("display", "none");
            var $scoreBestTrick5 = $(ElementHelper.getElementById($viewState, "score_best_trick_5"));
            $scoreBestTrick5.css("display", "none");
        } else {
            // hide score 1
            var $score1 = $(ElementHelper.getElementById($viewState, "score_1"));
            var $score1Text = $(ElementHelper.getElementById($score1, "text"));
            var $score1Value = $(ElementHelper.getElementById($score1, "value"));
            var $score1Background = $(ElementHelper.getElementById($score1, "background"));
            $score1.css("display", "none");
            $score1Text.css("display", "none");
            $score1Value.css("display", "none");
            $score1Background.css("display", "none");

            // prepare score 2
            var $score2 = $(ElementHelper.getElementById($viewState, "score_2"));
            var $score2Text = $(ElementHelper.getElementById($score2, "text"));
            var $score2Value = $(ElementHelper.getElementById($score2, "value"));
            var $score2Background = $(ElementHelper.getElementById($score2, "background"));
            $score2Text.css("color", "#f1f1f1");
            $score2Value.css("color", "#f1f1f1");
            $score2Background.attr("src", "../Content/Billboard/img/viewstate/viewstate_athlete_score/athlete_score_background_highlighted.png");

            // hide score 3
            var $score3 = $(ElementHelper.getElementById($viewState, "score_3"));
            var $score3Text = $(ElementHelper.getElementById($score3, "text"));
            var $score3Value = $(ElementHelper.getElementById($score3, "value"));
            var $score3Background = $(ElementHelper.getElementById($score3, "background"));
            $score3.css("display", "none");
            $score3Text.css("display", "none");
            $score3Value.css("display", "none");
            $score3Background.css("display", "none");

            $score2Text.html("Toplam<br />Puan");
            $score2Text.css("font-size", "34pt");
            $score2Text.css("top", "32px");
            $score2Text.css("left", "33px");
            $score2Text.css("line-height", "35pt");

            $score2Value.css("left", "200px");
            $score2Value.html(self.formatScore(totalScore, tournamentStyle));

            var $score1b = $(ElementHelper.getElementById($viewState, "score_1b"));
            $score1b.css("display", "block");
            var $score1bValue = $(ElementHelper.getElementById($score1b, "value"));
            $score1bValue.html(self.formatScore(score1, tournamentStyle));

            var $score2b = $(ElementHelper.getElementById($viewState, "score_2b"));
            $score2b.css("display", "block");
            var $score2bValue = $(ElementHelper.getElementById($score2b, "value"));
            $score2bValue.html(self.formatScore(score2, tournamentStyle));

            var $score3b = $(ElementHelper.getElementById($viewState, "score_3b"));
            $score3b.css("display", tournamePhaseTourCount == 3 ? "block" : "none");
            var $score3bValue = $(ElementHelper.getElementById($score3b, "value"));
            $score3bValue.html(self.formatScore(score3, tournamentStyle));

            var $scoreBestTrick1 = $(ElementHelper.getElementById($viewState, "score_best_trick_1"));
            $scoreBestTrick1.css("display", "block");
            var $scoreBestTrick2 = $(ElementHelper.getElementById($viewState, "score_best_trick_2"));
            $scoreBestTrick2.css("display", "block");
            var $scoreBestTrick3 = $(ElementHelper.getElementById($viewState, "score_best_trick_3"));
            $scoreBestTrick3.css("display", "block");
            var $scoreBestTrick4 = $(ElementHelper.getElementById($viewState, "score_best_trick_4"));
            $scoreBestTrick4.css("display", "block");
            var $scoreBestTrick5 = $(ElementHelper.getElementById($viewState, "score_best_trick_5"));
            $scoreBestTrick5.css("display", "block");
        }

        // order
        var order = ViewStateHelper.getProperty(properties, "order");
        var $order = $(ElementHelper.getElementById($viewState, "order"));
        var $orderValue = $(ElementHelper.getElementById($order, "value"));
        $orderValue.html(order + ".");

        // under age
        var $helmet = $(ElementHelper.getElementById($viewState, "helmet"));
        var underAge = ViewStateHelper.getProperty(properties, "under_age");
        if (underAge)
            $helmet.css("display", "block");
        else
            $helmet.css("display", "none");

        // highlight normal scores (b ones)
        this.highlightScore($viewState, score1, score2, score3);

        // highlight 4 high scores after best trick 2
        if (tournamentTour >= 10 + bestTrickPointCount) {
            var scoreDatas = [];
            scoreDatas.push({ score: bestTrick1, name: "best_trick_1" });
            scoreDatas.push({ score: bestTrick2, name: "best_trick_2" });
            scoreDatas.push({ score: bestTrick3, name: "best_trick_3" });
            scoreDatas.push({ score: bestTrick4, name: "best_trick_4" });
            scoreDatas.push({ score: bestTrick5, name: "best_trick_5" });

            // sort scores
            for (var i = 0; i < scoreDatas.length; i++) {
                for (var j = 0; j < scoreDatas.length - 1; j++) {
                    if (scoreDatas[j].score < scoreDatas[j + 1].score) {
                        var score = scoreDatas[j];
                        scoreDatas[j] = scoreDatas[j + 1];
                        scoreDatas[j + 1] = score;
                    }
                }
            }

            console.info("scores > " + scoreDatas);

            // highlight best tricks
            var count = 0;
            for (var i = 0; i < scoreDatas.length; i++) {
                var scoreData = scoreDatas[i];
                if (count < bestTrickPointCount) {
                    if (scoreData.score != 0) {
                        count++;
                        this.highlightBestTrickScore($viewState, scoreData, true);
                    }
                    else
                        this.highlightBestTrickScore($viewState, scoreData, false);
                } else {
                    this.highlightBestTrickScore($viewState, scoreData, false);
                }
            }
        }

    }

    this.highlightScore = function ($viewState, score1, score2, score3) {
        var $score1b = $(ElementHelper.getElementById($viewState, "score_1b"));
        var $score1bValue = $(ElementHelper.getElementById($score1b, "value"));
        var $score2b = $(ElementHelper.getElementById($viewState, "score_2b"));
        var $score2bValue = $(ElementHelper.getElementById($score2b, "value"));
        var $score3b = $(ElementHelper.getElementById($viewState, "score_3b"));
        var $score3bValue = $(ElementHelper.getElementById($score3b, "value"));

        $score1bValue.css("color", "#000000");
        $score2bValue.css("color", "#000000");
        $score3bValue.css("color", "#000000");

        if (score1 > score2 && score1 > score3)
            $score1bValue.css("color", "#ff0000");
        else if (score2 > score1 && score2 > score3)
            $score2bValue.css("color", "#ff0000");
        else if (score3 > score1 && score3 > score2)
            $score3bValue.css("color", "#ff0000");
    }

    this.highlightBestTrickScore = function ($viewState, scoreData, highlightState) {
        if (scoreData.name == "best_trick_1") {
            var $bestTrick1 = $(ElementHelper.getElementById($viewState, "score_best_trick_1"));
            var $bestTrick1Value = $(ElementHelper.getElementById($bestTrick1, "value"));
            if (highlightState)
                $bestTrick1Value.css("color", "#ff0000");
            else
                $bestTrick1Value.css("color", "#000000");
        } else if (scoreData.name == "best_trick_2") {
            var $bestTrick2 = $(ElementHelper.getElementById($viewState, "score_best_trick_2"));
            var $bestTrick2Value = $(ElementHelper.getElementById($bestTrick2, "value"));
            if (highlightState)
                $bestTrick2Value.css("color", "#ff0000");
            else
                $bestTrick2Value.css("color", "#000000");
        } else if (scoreData.name == "best_trick_3") {
            var $bestTrick3 = $(ElementHelper.getElementById($viewState, "score_best_trick_3"));
            var $bestTrick3Value = $(ElementHelper.getElementById($bestTrick3, "value"));
            if (highlightState)
                $bestTrick3Value.css("color", "#ff0000");
            else
                $bestTrick3Value.css("color", "#000000");
        } else if (scoreData.name == "best_trick_4") {
            var $bestTrick4 = $(ElementHelper.getElementById($viewState, "score_best_trick_4"));
            var $bestTrick4Value = $(ElementHelper.getElementById($bestTrick4, "value"));
            if (highlightState)
                $bestTrick4Value.css("color", "#ff0000");
            else
                $bestTrick4Value.css("color", "#000000");
        } else if (scoreData.name == "best_trick_5") {
            var $bestTrick5 = $(ElementHelper.getElementById($viewState, "score_best_trick_5"));
            var $bestTrick5Value = $(ElementHelper.getElementById($bestTrick5, "value"));
            if (highlightState)
                $bestTrick5Value.css("color", "#ff0000");
            else
                $bestTrick5Value.css("color", "#000000");
        }
    }

    this.deinitialize = function () {

    }
}