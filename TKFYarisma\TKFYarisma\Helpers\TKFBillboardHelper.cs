﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Mvc;
using TKFYarisma.Enum;
using TKFYarisma.Models;

namespace TKFYarisma.Helpers
{
    public class TKFBillboardHelper
    {
        public static Tournament GetSelectedTournament(TKFYarismaEntities dbContext)
        {
            return (from table in dbContext.Tournaments where table.Selected == true select table).SingleOrDefault();
        }

        public static Billboard GetBillboard(TKFYarismaEntities dbContext)
        {
            return (from table in dbContext.Billboards select table).FirstOrDefault();
        }

        public static TournamentGroupAthlete GetSelectedTournamentGroupAthlete(TKFYarismaEntities dbContext, TournamentPhas tournamentPhase, TournamentGroup tournamentGroup)
        {
            return (from table in dbContext.TournamentGroupAthletes
                    where table.PhaseId == tournamentPhase.Id &&
                          table.GroupId == tournamentGroup.Id &&
                          table.Selected == true
                    select table).SingleOrDefault();
        }

        public static Athlete GetAthlete(TKFYarismaEntities dbContext, TournamentGroupAthlete tournamentGroupAthlete)
        {
            return (from table in dbContext.Athletes where table.Id == tournamentGroupAthlete.AthleteId select table).SingleOrDefault();
        }

        public static Athlete GetAthlete(TKFYarismaEntities dbContext, int? id)
        {
            return (from table in dbContext.Athletes where table.Id == id select table).SingleOrDefault();
        }

        public static TournamentGroup GetSelectedTournamentGroup(TKFYarismaEntities dbContext, Tournament tournament)
        {
            return (from table in dbContext.TournamentGroups where table.TournamentId == tournament.Id && table.Selected == true select table).SingleOrDefault();
        }

        public static TournamentPhas GetSelectedTournamentPhase(TKFYarismaEntities dbContext, Tournament tournament)
        {
            return (from table in dbContext.TournamentPhases where table.TournamentId == tournament.Id && table.Selected == true select table).SingleOrDefault();
        }

        public static TournamentPhas GetLastTournamentPhase(TKFYarismaEntities dbContext, Tournament tournament)
        {
            var tournamentPhases = (from table in dbContext.TournamentPhases
                                    where table.TournamentId == tournament.Id
                                    orderby table.PhaseOrder
                                    select table).ToList();

            if (tournamentPhases.Count > 0)
                return tournamentPhases[tournamentPhases.Count - 1];
            else
                return null;
        }

        public static ContentResult GetViewStateDefault(TKFYarismaEntities dbContext)
        {
            var billboard = GetBillboard(dbContext);

            return new ContentResult()
            {
                Content = new JObject()
                {
                    ["viewState"] = billboard.ViewState
                }.ToString(),
                ContentType = "application/json",
                ContentEncoding = Encoding.UTF8
            };
        }

        public static ContentResult GetViewStateAthleteOrders(TKFYarismaEntities dbContext)
        {
            var tournament = GetSelectedTournament(dbContext);
            var tournamentPhase = GetSelectedTournamentPhase(dbContext, tournament);
            var tournamentGroup = GetSelectedTournamentGroup(dbContext, tournament);

            var properties = new Dictionary<string, object>();
            properties.Add("tournament_name_and_phase", GetTournamentName(dbContext) + " - " + GetTournamentPhaseName(dbContext) + " - " + GetTournamentTourName(tournament));

            var tournamentGroupAthletes = (from table in dbContext.TournamentGroupAthletes
                                           where table.PhaseId == tournamentPhase.Id &&
                                                 table.GroupId == tournamentGroup.Id
                                           orderby table.AthleteOrder
                                           select table).ToList();

            var jArrayAthletes = new JArray();

            foreach (var tournamentGroupAthlete in tournamentGroupAthletes)
            {
                var athlete = (from table in dbContext.Athletes
                               where table.Id == tournamentGroupAthlete.AthleteId
                               select table).SingleOrDefault();

                if (athlete != null)
                {
                    var jObjectAthlete = new JObject();

                    jObjectAthlete["athlete_order"] = tournamentGroupAthlete.AthleteOrder;
                    jObjectAthlete["athlete_name"] = string.Format("{0} {1}", athlete.FirstName, athlete.LastName);

                    var districtName = GetAthleteDistrictName(dbContext, athlete.DistrictId);
                    jObjectAthlete["athlete_district_name"] = districtName;
                    var cityName = GetAthleteCityName(dbContext, athlete.CityId);
                    jObjectAthlete["athlete_city_name"] = cityName;

                    jArrayAthletes.Add(jObjectAthlete);
                }
            }

            properties.Add("athletes", jArrayAthletes);

            var tour = tournament.Tour != null ? tournament.Tour.Value : 1;
            properties.Add("tour", tour);

            return new ContentResult()
            {
                Content = new JObject()
                {
                    ["viewState"] = "athlete_orders",
                    ["viewData"] = SerializeDictionary(properties)
                }.ToString(),
                ContentType = "application/json",
                ContentEncoding = Encoding.UTF8
            };
        }

        private static string GetAthleteDistrictName(TKFYarismaEntities dbContext, Athlete athlete)
        {
            var district = (from table in dbContext.Districts
                            where table.Id == athlete.DistrictId
                            select table).SingleOrDefault();

            return district != null ? district.Name : string.Empty;
        }

        private static string GetAthleteDistrictName(TKFYarismaEntities dbContext, int? id)
        {
            var district = (from table in dbContext.Districts
                            where table.Id == id
                            select table).SingleOrDefault();

            return district != null ? district.Name : string.Empty;
        }

        private static string GetAthleteCityName(TKFYarismaEntities dbContext, Athlete athlete)
        {
            var city = (from table in dbContext.DistrictCities
                        where table.Id == athlete.CityId
                        select table).SingleOrDefault();

            return city != null ? city.Name : string.Empty;
        }

        private static string GetAthleteCityName(TKFYarismaEntities dbContext, int? id)
        {
            var city = (from table in dbContext.DistrictCities
                        where table.Id == id
                        select table).SingleOrDefault();

            return city != null ? city.Name : string.Empty;
        }

        private static string GetTournamentName(TKFYarismaEntities dbContext)
        {
            var result = string.Empty;

            var tournament = GetSelectedTournament(dbContext);
            if (tournament != null)
            {
                if (tournament.Style == 1)
                    result += "Sokak Disiplini";
                else if (tournament.Style == 2)
                    result += "Park Disiplini";
            }

            var tournamentGroup = GetSelectedTournamentGroup(dbContext, tournament);
            if (tournamentGroup != null)
                result += " " + tournamentGroup.Name;

            result += " " + tournament.DisplayNameSuffix;

            return result;
        }

        private static string GetTournamentPhaseName(TKFYarismaEntities dbContext)
        {
            var result = string.Empty;

            var tournament = GetSelectedTournament(dbContext);
            if (tournament != null)
            {
                var tournamentPhase = GetSelectedTournamentPhase(dbContext, tournament);
                if (tournamentPhase != null)
                    result += tournamentPhase.Name;
            }

            return result;
        }

        private static string GetTournamentTourName(Tournament tournament)
        {
            var tour = tournament.Tour != null ? tournament.Tour.Value : 0;

            if (tour == 11)
                return "1. Best Trick";
            else if (tour == 12)
                return "2. Best Trick";
            else if (tour == 13)
                return "3. Best Trick";
            else if (tour == 14)
                return "4. Best Trick";
            else if (tour == 15)
                return "5. Best Trick";
            else
                return tour.ToString() + ".Tur";
        }

        public static ContentResult GetViewStateAthleteInformation(TKFYarismaEntities dbContext)
        {
            var tournament = GetSelectedTournament(dbContext);
            var tournamentPhase = GetSelectedTournamentPhase(dbContext, tournament);
            var tournamentGroup = GetSelectedTournamentGroup(dbContext, tournament);
            var tournamentGroupAthlete = GetSelectedTournamentGroupAthlete(dbContext, tournamentPhase, tournamentGroup);
            var athlete = GetAthlete(dbContext, tournamentGroupAthlete);

            var properties = new Dictionary<string, object>();
            properties.Add("tournament_name_and_phase", GetTournamentName(dbContext) + " - " + GetTournamentPhaseName(dbContext) + " - " + GetTournamentTourName(tournament));

            var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);
            properties.Add("athlete_photo", urlHelper.Content("~/Uploads/Athlete/Photo/" + athlete.PhotoFilename));
            properties.Add("athlete_name", string.Format("{0} {1}", athlete.FirstName, athlete.LastName));

            var districtName = GetAthleteDistrictName(dbContext, athlete);
            var cityName = GetAthleteCityName(dbContext, athlete);
            properties.Add("athlete_district_and_city", string.Format("{0} {1}", districtName, cityName));

            if (athlete.BirthDate != null)
            {
                var age = (DateTime.Today - athlete.BirthDate.Value).Days / 365.25m;

                properties.Add("under_age", age < 18);
            }

            return new ContentResult()
            {
                Content = new JObject()
                {
                    ["viewState"] = "athlete_info",
                    ["viewData"] = SerializeDictionary(properties)
                }.ToString(),
                ContentType = "application/json",
                ContentEncoding = Encoding.UTF8
            };
        }

        public static ContentResult GetViewStateAthleteTimer(TKFYarismaEntities dbContext)
        {
            var tournament = GetSelectedTournament(dbContext);
            var tournamentPhase = GetSelectedTournamentPhase(dbContext, tournament);
            var tournamentGroup = GetSelectedTournamentGroup(dbContext, tournament);
            var tournamentGroupAthlete = GetSelectedTournamentGroupAthlete(dbContext, tournamentPhase, tournamentGroup);
            var athlete = GetAthlete(dbContext, tournamentGroupAthlete);

            var properties = new Dictionary<string, object>();
            properties.Add("tournament_name_and_phase", GetTournamentName(dbContext) + " - " + GetTournamentPhaseName(dbContext) + " - " + GetTournamentTourName(tournament));

            var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);
            properties.Add("athlete_photo", urlHelper.Content("~/Uploads/Athlete/Photo/" + athlete.PhotoFilename));
            properties.Add("athlete_name", string.Format("{0} {1}", athlete.FirstName, athlete.LastName));

            var districtName = GetAthleteDistrictName(dbContext, athlete);
            var cityName = GetAthleteCityName(dbContext, athlete);
            properties.Add("athlete_district_and_city", string.Format("{0} {1}", districtName, cityName));

            properties.Add("total_time", tournament.ScoringInterval);

            if (tournament.Tour != null && tournament.Tour == 2)
            {
                var tournamentGroupAthleteScore = (from table in dbContext.TournamentGroupAthleteScores
                                                   where table.PhaseId == tournamentPhase.Id &&
                                                         table.GroupId == tournamentGroup.Id &&
                                                         table.Tour == 1 &&
                                                         table.AthleteId == athlete.Id
                                                   select table).SingleOrDefault();
                if (tournamentGroupAthleteScore != null)
                {
                    double previousScore = tournamentGroupAthleteScore.Score != null ? tournamentGroupAthleteScore.Score.Value : -1;
                    if (previousScore != -1)
                        properties.Add("previous_score", previousScore.ToString("0.00"));
                }
            }

            var billboard = GetBillboard(dbContext);
            properties.Add("timer_started", billboard.TimerStarted);
            properties.Add("timer_finished", billboard.TimerFinished);

            properties.Add("end_phase", tournamentPhase.EndPhase == true);

            if (athlete.BirthDate != null)
            {
                var age = (DateTime.Today - athlete.BirthDate.Value).Days / 365.25m;

                properties.Add("under_age", age < 18);
            }

            return new ContentResult()
            {
                Content = new JObject()
                {
                    ["viewState"] = "athlete_timer",
                    ["viewData"] = SerializeDictionary(properties)
                }.ToString(),
                ContentType = "application/json",
                ContentEncoding = Encoding.UTF8
            };
        }

        public static ContentResult GetViewStateAthleteScore(TKFYarismaEntities dbContext)
        {
            var tournament = GetSelectedTournament(dbContext);
            var tournamentPhase = GetSelectedTournamentPhase(dbContext, tournament);
            var tournamentGroup = GetSelectedTournamentGroup(dbContext, tournament);
            var tournamentGroupAthlete = GetSelectedTournamentGroupAthlete(dbContext, tournamentPhase, tournamentGroup);
            var athlete = GetAthlete(dbContext, tournamentGroupAthlete);

            var properties = new Dictionary<string, object>();
            properties.Add("tournament_name_and_phase", GetTournamentName(dbContext) + " - " + GetTournamentPhaseName(dbContext) + " - " + GetTournamentTourName(tournament));
            properties.Add("tournament_style", tournament.Style);
            properties.Add("tournament_tour", tournament.Tour);
            properties.Add("tournament_best_trick", tournament.BestTrick);
            properties.Add("tournament_best_trick_point_count", tournament.BestTrickPointCount);

            var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);
            properties.Add("athlete_photo", urlHelper.Content("~/Uploads/Athlete/Photo/" + athlete.PhotoFilename));
            properties.Add("athlete_name", string.Format("{0} {1}", athlete.FirstName, athlete.LastName));

            var districtName = GetAthleteDistrictName(dbContext, athlete);
            var cityName = GetAthleteCityName(dbContext, athlete);
            properties.Add("athlete_district_and_city", string.Format("{0} {1}", districtName, cityName));

            var tour = tournament.Tour != null ? tournament.Tour.Value : 1;
            properties.Add("tour", tour);

            properties.Add("end_phase", tournamentPhase.EndPhase == true);
            properties.Add("phase_tour_count", tournamentPhase.TourCount);

            var athleteScores = (from table in dbContext.ProcedureGetAthleteScores(tournamentPhase.Id, tournamentGroup.Id)
                                 select table).ToList();

            if (tournamentPhase.EndPhase != true)
            {
                var orderedAthleteScores = (from table in athleteScores
                                            select new
                                            {
                                                Id = table.Id,
                                                AthleteId = table.AthleteId,
                                                FirstName = table.FirstName,
                                                LastName = table.LastName,
                                                DistrictId = table.DistrictId,
                                                CityId = table.CityId,
                                                Score1 = table.Score1,
                                                Score2 = table.Score2,
                                                Score3 = table.Score3,
                                                Score = ScoreHelper.GetHighestScore(table.Score1, table.Score2, table.Score3)
                                            }).OrderByDescending(d => d.Score).ToList();

                var index = 1;
                foreach (var orderedAthleteScore in orderedAthleteScores)
                {
                    if (orderedAthleteScore.AthleteId == athlete.Id)
                    {
                        properties.Add("score_1", orderedAthleteScore.Score1);
                        properties.Add("score_2", orderedAthleteScore.Score2);
                        properties.Add("score_3", orderedAthleteScore.Score3);
                        properties.Add("score", orderedAthleteScore.Score);
                        properties.Add("order", index);

                        break;
                    }

                    index++;
                }
            }
            else
            {
                var athleteScoreInfos = new List<AthleteScoreInfo>();

                foreach (var athleteScore in athleteScores)
                {
                    var highestScore = ScoreHelper.GetHighestScore(athleteScore.Score1, athleteScore.Score2, athleteScore.Score3);

                    var totalScore = highestScore;

                    if (tournament.BestTrick == true)
                    {
                        var scores = new List<double>();
                        scores.Add(athleteScore.BestTrick1);
                        scores.Add(athleteScore.BestTrick2);
                        scores.Add(athleteScore.BestTrick3);
                        scores.Add(athleteScore.BestTrick4);
                        scores.Add(athleteScore.BestTrick5);
                        scores.Sort();

                        totalScore += ScoreHelper.GetBestTrickSummary(scores, tournament.BestTrickPointCount);
                    }

                    athleteScoreInfos.Add(new AthleteScoreInfo()
                    {
                        AthleteId = athleteScore.AthleteId != null ? athleteScore.AthleteId.Value : 0,
                        Score1 = athleteScore.Score1,
                        Score2 = athleteScore.Score2,
                        Score3 = athleteScore.Score3,
                        BestTrick1 = athleteScore.BestTrick1,
                        BestTrick2 = athleteScore.BestTrick2,
                        BestTrick3 = athleteScore.BestTrick3,
                        BestTrick4 = athleteScore.BestTrick4,
                        BestTrick5 = athleteScore.BestTrick5,
                        TotalScore = totalScore
                    });
                }

                var index = 1;
                foreach (var orderedAthleteScoreInfo in athleteScoreInfos.OrderByDescending(d => d.TotalScore))
                {
                    if (orderedAthleteScoreInfo.AthleteId == athlete.Id)
                    {
                        properties.Add("score_1", orderedAthleteScoreInfo.Score1);
                        properties.Add("score_2", orderedAthleteScoreInfo.Score2);
                        properties.Add("score_3", orderedAthleteScoreInfo.Score3);
                        
                        properties.Add("best_trick_1", orderedAthleteScoreInfo.BestTrick1);
                        properties.Add("best_trick_2", orderedAthleteScoreInfo.BestTrick2);
                        properties.Add("best_trick_3", orderedAthleteScoreInfo.BestTrick3);
                        properties.Add("best_trick_4", orderedAthleteScoreInfo.BestTrick4);
                        properties.Add("best_trick_5", orderedAthleteScoreInfo.BestTrick5);

                        properties.Add("total_score", orderedAthleteScoreInfo.TotalScore);

                        properties.Add("order", index);

                        break;
                    }

                    index++;
                }
            }

            if (athlete.BirthDate != null)
            {
                var age = (DateTime.Today - athlete.BirthDate.Value).Days / 365.25m;

                properties.Add("under_age", age < 18);
            }

            return new ContentResult()
            {
                Content = new JObject()
                {
                    ["viewState"] = "athlete_score",
                    ["viewData"] = SerializeDictionary(properties)
                }.ToString(),
                ContentType = "application/json",
                ContentEncoding = Encoding.UTF8
            };
        }

        public static ContentResult GetViewStateAthleteScores(TKFYarismaEntities dbContext)
        {
            var tournament = GetSelectedTournament(dbContext);
            var tournamentPhase = GetSelectedTournamentPhase(dbContext, tournament);
            var tournamentGroup = GetSelectedTournamentGroup(dbContext, tournament);

            var properties = new Dictionary<string, object>();
            properties.Add("tournament_name_and_phase", GetTournamentName(dbContext) + " - " + GetTournamentPhaseName(dbContext) + " - " + GetTournamentTourName(tournament));
            properties.Add("tournament_style", tournament.Style);
            properties.Add("tournament_tour", tournament.Tour);
            properties.Add("tournament_best_trick", tournament.BestTrick);

            var scores = new JArray();

            var athleteScores = (from table in dbContext.ProcedureGetAthleteScores(tournamentPhase.Id, tournamentGroup.Id)
                                 select table).ToList();

            if (tournamentPhase.EndPhase != true)
            {
                var orderedAthleteScores = (from table in athleteScores
                                            select new
                                            {
                                                Id = table.Id,
                                                AthleteId = table.AthleteId,
                                                FirstName = table.FirstName,
                                                LastName = table.LastName,
                                                DistrictId = table.DistrictId,
                                                CityId = table.CityId,
                                                Score1 = table.Score1,
                                                Score2 = table.Score2,
                                                Score3 = table.Score3,
                                                Score = ScoreHelper.GetHighestScore(table.Score1, table.Score2, table.Score3),
                                                Queued = table.Queued
                                            }).OrderByDescending(d => d.Score).ToList();

                var index = 1;
                foreach (var athleteScore in orderedAthleteScores)
                {
                    var score = new JObject();

                    score["athlete_order"] = index;
                    var athleteName = string.Format("{0} {1}", athleteScore.FirstName, athleteScore.LastName);
                    if (athleteName.Length > 15)
                    {
                        if (!string.IsNullOrEmpty(athleteScore.FirstName))
                        {
                            var firstName = string.Empty;

                            var firstNameTokens = athleteScore.FirstName.Split(' ');
                            if (firstNameTokens.Length == 2)
                            {
                                firstName += firstNameTokens[0].Substring(0, 1) + ". " + firstNameTokens[1];
                            }
                            else if (firstNameTokens.Length > 2)
                            {
                                firstName += firstNameTokens[firstNameTokens.Length - 1];
                            }
                            else
                                firstName = athleteScore.FirstName;

                            athleteName = string.Format("{0} {1}", firstName, athleteScore.LastName);

                            //if (athleteName.Length > 15)
                            //    athleteName = athleteName.Substring(0, 15) + "...";
                        }
                    }
                    score["athlete_name"] = athleteName;

                    var districtName = GetAthleteDistrictName(dbContext, athleteScore.DistrictId);
                    score["athlete_district_name"] = districtName;
                    var cityName = GetAthleteCityName(dbContext, athleteScore.CityId);
                    score["athlete_city_name"] = cityName;

                    score["score_1"] = athleteScore.Score1;
                    score["score_2"] = athleteScore.Score2;
                    score["score_3"] = athleteScore.Score3;
                    score["is_queued"] = athleteScore.Queued == true;
                    score["score"] = athleteScore.Score;

                    scores.Add(score);

                    index++;
                }
            }
            else
            {
                var athleteScoreInfos = new List<AthleteScoreInfo>();

                foreach (var athleteScore in athleteScores)
                {
                    var highestScore = ScoreHelper.GetHighestScore(athleteScore.Score1, athleteScore.Score2, athleteScore.Score3);

                    var totalScore = highestScore;

                    if (tournament.BestTrick == true)
                    {
                        var orderedScores = new List<double>();

                        orderedScores.Add(athleteScore.BestTrick1);
                        orderedScores.Add(athleteScore.BestTrick2);
                        orderedScores.Add(athleteScore.BestTrick3);
                        orderedScores.Add(athleteScore.BestTrick4);
                        orderedScores.Add(athleteScore.BestTrick5);
                        orderedScores.Sort();

                        totalScore += ScoreHelper.GetBestTrickSummary(orderedScores, tournament.BestTrickPointCount);
                    }

                    athleteScoreInfos.Add(new AthleteScoreInfo()
                    {
                        AthleteId = athleteScore.AthleteId != null ? athleteScore.AthleteId.Value : 0,
                        FirstName = athleteScore.FirstName,
                        LastName = athleteScore.LastName,
                        CityId = athleteScore.CityId,
                        DistrictId = athleteScore.DistrictId,
                        Score1 = athleteScore.Score1,
                        Score2 = athleteScore.Score2,
                        Score3 = athleteScore.Score3,
                        BestTrick1 = athleteScore.BestTrick1,
                        BestTrick2 = athleteScore.BestTrick2,
                        BestTrick3 = athleteScore.BestTrick3,
                        BestTrick4 = athleteScore.BestTrick4,
                        BestTrick5 = athleteScore.BestTrick5,
                        TotalScore = totalScore,
                        Queued = athleteScore.Queued
                    });
                }

                var index = 1;
                foreach (var orderedAthleteScoreInfo in athleteScoreInfos.OrderByDescending(d => d.TotalScore))
                {
                    var score = new JObject();

                    score["athlete_order"] = index;
                    var athleteName = string.Format("{0} {1}", orderedAthleteScoreInfo.FirstName, orderedAthleteScoreInfo.LastName);
                    if (athleteName.Length > 15)
                    {
                        if (!string.IsNullOrEmpty(orderedAthleteScoreInfo.FirstName))
                        {
                            var firstName = string.Empty;

                            var firstNameTokens = orderedAthleteScoreInfo.FirstName.Split(' ');
                            if (firstNameTokens.Length == 2)
                            {
                                firstName += firstNameTokens[0].Substring(0, 1) + ". " + firstNameTokens[1];
                            }
                            else if (firstNameTokens.Length > 2)
                            {
                                firstName += firstNameTokens[firstNameTokens.Length - 1];
                            }
                            else
                                firstName = orderedAthleteScoreInfo.FirstName;

                            athleteName = string.Format("{0} {1}", firstName, orderedAthleteScoreInfo.LastName);

                            //if (athleteName.Length > 15)
                            //    athleteName = athleteName.Substring(0, 15) + "...";
                        }
                    }
                    score["athlete_name"] = athleteName;

                    var districtName = GetAthleteDistrictName(dbContext, orderedAthleteScoreInfo.DistrictId);
                    score["athlete_district_name"] = districtName;
                    var cityName = GetAthleteCityName(dbContext, orderedAthleteScoreInfo.CityId);
                    score["athlete_city_name"] = cityName;

                    score["score_1"] = orderedAthleteScoreInfo.Score1;
                    score["score_2"] = orderedAthleteScoreInfo.Score2;
                    score["score_3"] = orderedAthleteScoreInfo.Score3;
                    score["best_trick_1"] = orderedAthleteScoreInfo.BestTrick1;
                    score["best_trick_2"] = orderedAthleteScoreInfo.BestTrick2;
                    score["best_trick_3"] = orderedAthleteScoreInfo.BestTrick3;
                    score["best_trick_4"] = orderedAthleteScoreInfo.BestTrick4;
                    score["best_trick_5"] = orderedAthleteScoreInfo.BestTrick5;
                    score["total_score"] = orderedAthleteScoreInfo.TotalScore;
                    score["is_queued"] = orderedAthleteScoreInfo.Queued == true;

                    scores.Add(score);

                    index++;
                }
            }

            properties.Add("scores", scores);

            var tour = tournament.Tour != null ? tournament.Tour.Value : 1;
            properties.Add("tour", tour);

            properties.Add("end_phase", tournamentPhase.EndPhase == true);
            properties.Add("phase_tour_count", tournamentPhase.TourCount);

            return new ContentResult()
            {
                Content = new JObject()
                {
                    ["viewState"] = "athlete_scores",
                    ["viewData"] = SerializeDictionary(properties)
                }.ToString(),
                ContentType = "application/json",
                ContentEncoding = Encoding.UTF8
            };
        }

        public static ContentResult GetViewStateMedalCeromony(TKFYarismaEntities dbContext)
        {
            var tournament = GetSelectedTournament(dbContext);
            var tournamentPhase = GetLastTournamentPhase(dbContext, tournament);
            var tournamentGroup = GetSelectedTournamentGroup(dbContext, tournament);
            var tournamentGroupAthlete = GetSelectedTournamentGroupAthlete(dbContext, tournamentPhase, tournamentGroup);

            var properties = new Dictionary<string, object>();
            properties.Add("tournament_name", GetTournamentName(dbContext));

            var tournamentGroupMedals = (from table in dbContext.TournamentGroupMedals where table.GroupId == tournamentGroup.Id select table).SingleOrDefault();

            var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);

            var athleteScores_ = (from table in dbContext.ProcedureGetAthleteScores(tournamentPhase.Id, tournamentGroup.Id)
                                 select table).ToList();

            var athleteScoreInfos = new List<AthleteScoreInfo>();

            foreach (var athleteScore in athleteScores_)
            {
                var highestScore = ScoreHelper.GetHighestScore(athleteScore.Score1, athleteScore.Score2, athleteScore.Score3);

                var totalScore = highestScore;

                if (tournament.BestTrick == true)
                {
                    var orderedScores = new List<double>();

                    orderedScores.Add(athleteScore.BestTrick1);
                    orderedScores.Add(athleteScore.BestTrick2);
                    orderedScores.Add(athleteScore.BestTrick3);
                    orderedScores.Add(athleteScore.BestTrick4);
                    orderedScores.Add(athleteScore.BestTrick5);
                    orderedScores.Sort();

                    totalScore += ScoreHelper.GetBestTrickSummary(orderedScores, tournament.BestTrickPointCount);
                }

                var athleteScoreInfo = new AthleteScoreInfo()
                {
                    AthleteId = athleteScore.AthleteId != null ? athleteScore.AthleteId.Value : 0,
                    FirstName = athleteScore.FirstName,
                    LastName = athleteScore.LastName,
                    CityId = athleteScore.CityId,
                    DistrictId = athleteScore.DistrictId,
                    Score1 = athleteScore.Score1,
                    Score2 = athleteScore.Score2,
                    Score3 = athleteScore.Score3,
                    BestTrick1 = athleteScore.BestTrick1,
                    BestTrick2 = athleteScore.BestTrick2,
                    BestTrick3 = athleteScore.BestTrick3,
                    BestTrick4 = athleteScore.BestTrick4,
                    BestTrick5 = athleteScore.BestTrick5,
                    TotalScore = totalScore,
                    Queued = athleteScore.Queued
                };

                athleteScoreInfos.Add(athleteScoreInfo);
            }

            var athleteScores = athleteScoreInfos.OrderByDescending(d => d.TotalScore).ToList();

            if (athleteScores.Count >= 1)
            {
                var medalAthleteId = athleteScores[0].AthleteId;
                var medalAthlete = GetAthlete(dbContext, medalAthleteId);
                var medalProperties = new Dictionary<string, object>();
                medalProperties.Add("athlete_photo", urlHelper.Content("~/Uploads/Athlete/Photo/" + medalAthlete.PhotoFilename));
                medalProperties.Add("athlete_name", string.Format("{0} {1}", medalAthlete.FirstName, medalAthlete.LastName));
                properties.Add("gold_medal", medalProperties);
            }

            if (athleteScores.Count >= 2)
            {
                var medalAthleteId = athleteScores[1].AthleteId;
                var medalAthlete = GetAthlete(dbContext, medalAthleteId);
                var medalProperties = new Dictionary<string, object>();
                medalProperties.Add("athlete_photo", urlHelper.Content("~/Uploads/Athlete/Photo/" + medalAthlete.PhotoFilename));
                medalProperties.Add("athlete_name", string.Format("{0} {1}", medalAthlete.FirstName, medalAthlete.LastName));
                properties.Add("silver_medal", medalProperties);
            }

            if (athleteScores.Count >= 3)
            {
                var medalAthleteId = athleteScores[2].AthleteId;
                var medalAthlete = GetAthlete(dbContext, medalAthleteId);
                var medalProperties = new Dictionary<string, object>();
                medalProperties.Add("athlete_photo", urlHelper.Content("~/Uploads/Athlete/Photo/" + medalAthlete.PhotoFilename));
                medalProperties.Add("athlete_name", string.Format("{0} {1}", medalAthlete.FirstName, medalAthlete.LastName));
                properties.Add("bronze_medal", medalProperties);
            }

            var billboard = GetBillboard(dbContext);
            var medalCount = billboard.MedalCount;
            properties.Add("medal_count", medalCount);

            return new ContentResult()
            {
                Content = new JObject()
                {
                    ["viewState"] = "medal_ceromony",
                    ["viewData"] = SerializeDictionary(properties)
                }.ToString(),
                ContentType = "application/json",
                ContentEncoding = Encoding.UTF8
            };
        }

        private static TournamentVideo GetSelectedTournamentVideo(TKFYarismaEntities dbContext)
        {
            var tournament = GetSelectedTournament(dbContext);
            return (from table in dbContext.TournamentVideos
                    where table.TournamentId == tournament.Id && table.Selected == true
                    select table).SingleOrDefault();
        }

        private static string GetBaseUrl()
        {
            var request = HttpContext.Current.Request;
            var appUrl = HttpRuntime.AppDomainAppVirtualPath;

            var baseUrl = string.Format(@"{0}://{1}{2}", request.Url.Scheme, request.Url.Authority, appUrl);

            return baseUrl;
        }

        public static ContentResult GetViewStateVideoPlayer(TKFYarismaEntities dbContext)
        {
            var tournamentVideo = GetSelectedTournamentVideo(dbContext);

            var properties = new Dictionary<string, object>();

            var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);
            properties.Add("url", GetBaseUrl() + "/Uploads/Video/" + tournamentVideo.FileName);

            return new ContentResult()
            {
                Content = new JObject()
                {
                    ["viewState"] = "video_player",
                    ["viewData"] = SerializeDictionary(properties)
                }.ToString(),
                ContentType = "application/json",
                ContentEncoding = Encoding.UTF8
            };
        }

        public static ContentResult GetViewStateReferees(TKFYarismaEntities dbContext)
        {
            var tournament = GetSelectedTournament(dbContext);
            var tournamentPhase = GetSelectedTournamentPhase(dbContext, tournament);
            var tournamentGroup = GetSelectedTournamentGroup(dbContext, tournament);

            var properties = new Dictionary<string, object>();
            properties.Add("tournament_name", GetTournamentName(dbContext));

            var jArrayReferees = new JArray();

            var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);

            // president
            jArrayReferees.Add(new JObject()
            {
                ["referee_name"] = tournament.PresidentName,
                ["referee_type"] = "president",
                ["referee_photo"] = urlHelper.Content("~/Uploads/Federation/" + tournament.PresidentPhotoFileName)
            });

            // chairperson
            if (tournament.ChairpersonType == "custom")
            {
                jArrayReferees.Add(new JObject()
                {
                    ["referee_name"] = tournament.ChairpersonName,
                    ["referee_type"] = "chairperson",
                    ["referee_photo"] = urlHelper.Content("~/Uploads/Federation/" + tournament.ChairpersonPhotoFileName)
                });
            }
            else
            {
                if (tournament.ChairpersonRefereeId != null)
                {
                    var refereeChairperson = (from table in dbContext.Referees
                                              where table.Id == tournament.ChairpersonRefereeId
                                              select table).SingleOrDefault();
                    if (refereeChairperson != null)
                    {
                        jArrayReferees.Add(new JObject()
                        {
                            ["referee_name"] = refereeChairperson.FirstName + " " + refereeChairperson.LastName,
                            ["referee_type"] = "chairperson",
                            ["referee_photo"] = urlHelper.Content("~/Uploads/Referee/Photo/" + refereeChairperson.PhotoFilename)
                        });
                    }
                }
            }

            // observer
            if (tournament.ObserverType == "custom")
            {
                jArrayReferees.Add(new JObject()
                {
                    ["referee_name"] = tournament.ObserverName,
                    ["referee_type"] = "observer",
                    ["referee_photo"] = urlHelper.Content("~/Uploads/Federation/" + tournament.ObserverPhotoFileName)
                });
            }
            else
            {
                if (tournament.ObserverRefereeId != null)
                {
                    var refereeObserver = (from table in dbContext.Referees
                                           where table.Id == tournament.ObserverRefereeId
                                           select table).SingleOrDefault();
                    if (refereeObserver != null)
                    {
                        jArrayReferees.Add(new JObject()
                        {
                            ["referee_name"] = refereeObserver.FirstName + " " + refereeObserver.LastName,
                            ["referee_type"] = "observer",
                            ["referee_photo"] = urlHelper.Content("~/Uploads/Referee/Photo/" + refereeObserver.PhotoFilename)
                        });
                    }
                }
            }

            // chief
            if (tournament.ChiefRefereeId != null)
            {
                var refereeChief = (from table in dbContext.Referees
                                    where table.Id == tournament.ChiefRefereeId
                                    select table).SingleOrDefault();
                if (refereeChief != null)
                {
                    jArrayReferees.Add(new JObject()
                    {
                        ["referee_name"] = refereeChief.FirstName + " " + refereeChief.LastName,
                        ["referee_type"] = "chief",
                        ["referee_photo"] = urlHelper.Content("~/Uploads/Referee/Photo/" + refereeChief.PhotoFilename)
                    });
                }
            }

            // secretary
            if (tournament.SecretaryRefereeId != null)
            {
                var refereeSecretary = (from table in dbContext.Referees
                                        where table.Id == tournament.SecretaryRefereeId
                                        select table).SingleOrDefault();
                if (refereeSecretary != null)
                {
                    jArrayReferees.Add(new JObject()
                    {
                        ["referee_name"] = refereeSecretary.FirstName + " " + refereeSecretary.LastName,
                        ["referee_type"] = "secretary",
                        ["referee_photo"] = urlHelper.Content("~/Uploads/Referee/Photo/" + refereeSecretary.PhotoFilename)
                    });
                }
            }

            // timing
            var referee = GetGroupRefereeInfo(dbContext, tournamentPhase, tournamentGroup, RefereeType.Timing);
            if (referee != null)
            {
                jArrayReferees.Add(new JObject()
                {
                    ["referee_name"] = referee.FirstName + " " + referee.LastName,
                    ["referee_type"] = "timing",
                    ["referee_photo"] = urlHelper.Content("~/Uploads/Referee/Photo/" + referee.PhotoFilename)
                });
            }

            // scoring
            var referees = GetGroupScoringRefereeInfos(dbContext, tournamentPhase, tournamentGroup);
            if (referees != null)
            {
                var order = 0;
                foreach (var referee_ in referees)
                {
                    jArrayReferees.Add(new JObject()
                    {
                        ["referee_name"] = referee_.FirstName + " " + referee_.LastName,
                        ["referee_type"] = "scoring",
                        ["referee_order"] = order,
                        ["referee_photo"] = urlHelper.Content("~/Uploads/Referee/Photo/" + referee_.PhotoFilename)
                    });

                    order++;
                }
            }

            properties.Add("referees", jArrayReferees);

            return new ContentResult()
            {
                Content = new JObject()
                {
                    ["viewState"] = "referees",
                    ["viewData"] = SerializeDictionary(properties)
                }.ToString(),
                ContentType = "application/json",
                ContentEncoding = Encoding.UTF8
            };
        }

        private static Referee GetGroupRefereeInfo(TKFYarismaEntities dbContext, TournamentPhas tournamentPhase, TournamentGroup tournamentGroup, int refereeType, int refereeOrder = 0)
        {
            var tournamentGroupReferee = (from table in dbContext.TournamentGroupReferees
                                          where table.PhaseId == tournamentPhase.Id &&
                                                table.GroupId == tournamentGroup.Id &&
                                                table.RefereeType == refereeType &&
                                                (refereeOrder == 0 || table.RefereeOrder == refereeOrder)
                                          select table).SingleOrDefault();
            if (tournamentGroupReferee != null)
            {
                return (from table in dbContext.Referees
                        where table.Id == tournamentGroupReferee.RefereeId
                        select table).SingleOrDefault();
            }
            else
                return null;
        }

        private static List<Referee> GetGroupScoringRefereeInfos(TKFYarismaEntities dbContext, TournamentPhas tournamentPhase, TournamentGroup tournamentGroup)
        {
            var result = new List<Referee>();

            var tournamentGroupReferees = (from table in dbContext.TournamentGroupReferees
                                           where table.PhaseId == tournamentPhase.Id &&
                                                 table.GroupId == tournamentGroup.Id &&
                                                 table.RefereeType == 4
                                           orderby table.RefereeOrder
                                           select table).ToList();
            foreach (var tournamentGroupReferee in tournamentGroupReferees)
            {
                var referee = (from table in dbContext.Referees
                               where table.Id == tournamentGroupReferee.RefereeId
                               select table).SingleOrDefault();
                if (referee != null)
                    result.Add(referee);
            }

            return result;
        }

        private static JArray SerializeDictionary(Dictionary<string, object> dictionary)
        {
            var properties = new JArray();

            foreach (var key in dictionary.Keys)
            {
                var property = new JObject
                {
                    ["name"] = key
                };

                if (dictionary[key] is string)
                    property["value"] = (string)dictionary[key];
                else if (dictionary[key] is int)
                    property["value"] = (int)dictionary[key];
                else if (dictionary[key] is long)
                    property["value"] = (long)dictionary[key];
                else if (dictionary[key] is double)
                    property["value"] = (double)dictionary[key];
                else if (dictionary[key] is bool)
                    property["value"] = (bool)dictionary[key];
                else if (dictionary[key] is ArrayList arrayList)
                {
                    var jArray = new JArray();

                    for (int i = 0; i < arrayList.Count; i++)
                    {
                        var dictionary_ = (Dictionary<string, object>)arrayList[i];
                        jArray.Add(SerializeDictionary(dictionary_));
                    }

                    property["value"] = jArray;
                }
                else if (dictionary[key] is JArray jArray)
                    property["value"] = jArray;
                else if (dictionary[key] is Dictionary<string, object> dictionary_)
                    property["value"] = SerializeDictionary(dictionary_);

                properties.Add(property);
            }

            return properties;
        }
    }
}