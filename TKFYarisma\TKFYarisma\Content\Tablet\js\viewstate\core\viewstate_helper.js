ViewStateHelper = {};

ViewStateHelper.getViewStateController = function (viewStateControllers, viewStateName) {
    for (var i = 0; i < viewStateControllers.length; i++) {
        if (viewStateControllers[i].viewStateName == viewStateName)
            return viewStateControllers[i];
    }
    return undefined;
}

ViewStateHelper.getProperty = function (properties, propertyName) {
    if (properties != undefined) {
        for(var i = 0; i < properties.length; i++) {
            if (properties[i].name == propertyName)
                return properties[i].value;
        }
    }
    else
        return undefined;
}

ViewStateHelper.setProperty = function (viewStateName, propertyName, value) {
    var viewStates = $("body").find("viewstate#" + viewStateName);
    if (viewStates.length > 0) {
        var $viewState = $(viewStates[0]);
        var propertyElements = $viewState.find("[property-name='" + propertyName + "']");
        if (propertyElements.length > 0) {
            var $propertyElement = $(propertyElements[0]);
            if ($propertyElement.is("img")) {
                $propertyElement.attr("src", value);
            } else if ($propertyElement.is("span")) {
                $propertyElement.html(value);
            }
        }
    }
}

ViewStateHelper.getElementForProperty = function (viewStateName, propertyName) {
    var viewStates = $("body").find("viewstate#" + viewStateName);
    if (viewStates.length > 0) {
        var $viewState = $(viewStates[0]);
        var propertyElements = $viewState.find("[property-name='" + propertyName + "']");
        if (propertyElements.length > 0)
            return propertyElements[0];
        else
            return undefined;
    }
    else
        return undefined;
}

ViewStateHelper.isArray = function (obj) {
    return Object.prototype.toString.call(obj) === '[object Array]';
}

ViewStateHelper.applyBindingToElements = function ($viewState, properties, path) {
    if (properties != undefined && properties.length > 0) {
        for (var i = 0; i < properties.length; i++) {
            var property = properties[i];

            var searchText = "";
            if (path == "")
                searchText = property.name;
            else
                searchText = path + "/" + property.name;

            var propertyElements = $viewState.find("[property-name='" + searchText + "']");
            if (propertyElements.length > 0) {
                var $propertyElement = $(propertyElements[0]);
                if ($propertyElement.is("img")) {
                    $propertyElement.attr("src", property.value);
                } else if ($propertyElement.is("span")) {
                    $propertyElement.html(property.value);
                }
            }

            if (ViewStateHelper.isArray(property.value)) {
                var path_ = path != "" ? path + "/" + property.name : property.name;
                ViewStateHelper.applyBindingToElements($viewState, property.value, path_);
            }
        }
    }
}