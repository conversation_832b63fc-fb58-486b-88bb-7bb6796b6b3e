﻿@using TKFYarisma.Helpers

@model TKFYarisma.Models.AthleteModel

@{
    ViewBag.Title = "Sporcuları İçe Aktar";
    ViewBag.Section = "Athletes";
}

<div class="row">
    <div class="col">
        <div class="tile-header tile-header-big">
            <div class="row">
                <div class="col mr-auto">
                    <h3>Sporcuları İçe Aktar</h3>
                </div>
            </div>
        </div>
        <div class="tile">
            @using (Html.BeginForm("ImportAthletes", "ControlPanel", FormMethod.Post, new { enctype = "multipart/form-data" }))
            {
                <div class="row">
                    <div class="col">
                        <div class="row row-form">
                            <div class="col-form-label-medium">
                                <span class="align-middle">Excel Dosyası</span>
                            </div>
                            <div class="col">
                                <input type="file" name="file" id="file" class="form-control" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <div class="row mt-3">
                            <div class="col">
                                <div class="row row-form-footer">
                                    <input class="btn btn-primary" type="submit" value="İçe Aktar" />
                                    <a href="javascript: history.back();" class="btn btn-secondary ml-2">İptal</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>