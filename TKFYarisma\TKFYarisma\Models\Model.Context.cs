﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace TKFYarisma.Models
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    using System.Data.Entity.Core.Objects;
    using System.Linq;
    
    public partial class TKFYarismaEntities : DbContext
    {
        public TKFYarismaEntities()
            : base("name=TKFYarismaEntities")
        {
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        public virtual DbSet<Athlete> Athletes { get; set; }
        public virtual DbSet<Billboard> Billboards { get; set; }
        public virtual DbSet<ControlCenter> ControlCenters { get; set; }
        public virtual DbSet<DistrictCity> DistrictCities { get; set; }
        public virtual DbSet<District> Districts { get; set; }
        public virtual DbSet<Referee> Referees { get; set; }
        public virtual DbSet<Tablet> Tablets { get; set; }
        public virtual DbSet<TournamentDay> TournamentDays { get; set; }
        public virtual DbSet<TournamentGroupAthleteRefereeScore> TournamentGroupAthleteRefereeScores { get; set; }
        public virtual DbSet<TournamentGroupAthlete> TournamentGroupAthletes { get; set; }
        public virtual DbSet<TournamentGroupAthleteScore> TournamentGroupAthleteScores { get; set; }
        public virtual DbSet<TournamentGroupMedal> TournamentGroupMedals { get; set; }
        public virtual DbSet<TournamentGroupReferee> TournamentGroupReferees { get; set; }
        public virtual DbSet<TournamentGroup> TournamentGroups { get; set; }
        public virtual DbSet<TournamentGroupTeamAthlete> TournamentGroupTeamAthletes { get; set; }
        public virtual DbSet<TournamentGroupTeam> TournamentGroupTeams { get; set; }
        public virtual DbSet<TournamentPhas> TournamentPhases { get; set; }
        public virtual DbSet<TournamentReferee> TournamentReferees { get; set; }
        public virtual DbSet<Tournament> Tournaments { get; set; }
        public virtual DbSet<TournamentVideo> TournamentVideos { get; set; }
        public virtual DbSet<User> Users { get; set; }
        public virtual DbSet<ViewAthlete> ViewAthletes { get; set; }
        public virtual DbSet<ViewReferee> ViewReferees { get; set; }
        public virtual DbSet<ViewTournamentGroupAthlete> ViewTournamentGroupAthletes { get; set; }
        public virtual DbSet<ViewTournamentGroupReferee> ViewTournamentGroupReferees { get; set; }
        public virtual DbSet<ViewTournamentReferee> ViewTournamentReferees { get; set; }
        public virtual DbSet<ViewTournament> ViewTournaments { get; set; }
    
        public virtual ObjectResult<ProcedureGetAthleteScores_Result> ProcedureGetAthleteScores(Nullable<int> phaseId, Nullable<int> groupId)
        {
            var phaseIdParameter = phaseId.HasValue ?
                new ObjectParameter("phaseId", phaseId) :
                new ObjectParameter("phaseId", typeof(int));
    
            var groupIdParameter = groupId.HasValue ?
                new ObjectParameter("groupId", groupId) :
                new ObjectParameter("groupId", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<ProcedureGetAthleteScores_Result>("ProcedureGetAthleteScores", phaseIdParameter, groupIdParameter);
        }
    }
}
