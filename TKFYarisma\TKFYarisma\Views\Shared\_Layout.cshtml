﻿@using TKFYarisma.Helpers

<!doctype html>
<html lang="tr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="~/favicon.ico">

    <title>Türkiye Kaykay Federasyonu - @ViewBag.Title</title>

    @Styles.Render("~/Content/css")
    @Scripts.Render("~/bundles/modernizr")
    @Scripts.Render("~/bundles/jquery")
    @Styles.Render("~/Content/site")
    <link href="https://fonts.googleapis.com/css?family=Open+Sans" rel="stylesheet">
</head>

<body>

    <nav class="navbar navbar-expand-md navbar-dark bg-dark fixed-top">
        <a class="navbar-brand" href="#">
            <img src="@Url.Content("~/Content/Site/images/logo.png")" class="logo img-fluid" />
        </a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarsExampleDefault" aria-controls="navbarsExampleDefault" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <span class="navbar-title">
            YARIŞMA YÖNETİM SİSTEMİ
        </span>
        <div class="collapse navbar-collapse" id="navbarsExampleDefault">
            <ul class="navbar-nav mr-auto"></ul>
            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="https://example.com" id="dropdown01" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><span class="icon fa fa-user"></span>Doğan Çoruh</a>
                    <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdown01">
                        <a class="dropdown-item" href="@Url.Action("AccountSettings")"><span class="icon fa fa-gear"></span>Account Settings</a>
                        <a class="dropdown-item" href="@Url.Action("SignOut")"><span class="icon fa fa-sign-out"></span>Sign Out</a>
                    </div>
                </li>
            </ul>
        </div>
    </nav>

    @{ 
        var actionName = ViewContext.RouteData.GetRequiredString("action");
    }

    <main class="wrapper">
        <nav id="sidebar" class="sidebar">
            <ul class="sidebar-menu list-group">
                <li class="list-group-item @if (ViewBag.Section == "ControlCenter") { <text>active</text> }"><a href="@Url.Action("ControlCenter")"><span class="icon fas fa-chart-line"></span>Kontrol Merkezi</a></li>
                <li class="list-group-item @if (ViewBag.Section == "Tournaments") { <text>active</text> }"><span class="icon fas fa-cube"></span><a href="@Url.Action("Tournaments")">Yarışmalar</a></li>
                <li class="list-group-item @if (ViewBag.Section == "Athletes") { <text>active</text> }"><span class="icon fas fa-users"></span><a href="@Url.Action("Athletes")">Sporcular</a></li>
                <li class="list-group-item @if (ViewBag.Section == "Referees") { <text>active</text> }"><span class="icon fas fa-users"></span><a href="@Url.Action("Referees")">Hakemler</a></li>
                <li class="list-group-item @if (ViewBag.Section == "Preferences") { <text>active</text> }"><span class="icon fas fa-cog"></span><a href="@Url.Action("Preferences")">Seçenekler</a></li>
            </ul>
        </nav>
        <div class="container-fluid content">
            @if (ViewBag.Path != null && ViewBag.Path != string.Empty)
            {
            <div class="row">
                <div class="col no-padding">
                    <div class="breadcrumb flex-fill">
                        <span>
                            @MvcHtmlString.Create(ViewBag.Path) <br />
                        </span>
                    </div>
                </div>
            </div>
            }
            <div class="row pt-3">
                <div class="col">
                    @RenderBody()
                </div>
            </div>
        </div>
    </main><!-- /.container -->

    @Scripts.Render("~/bundles/bootstrap")
    @RenderSection("scripts", required: false)
</body>
</html>
