﻿using TKFYarisma.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace TKFYarisma.Helpers
{
    public class PathHelper
    {
        public static string Tournaments()
        {
            return string.Empty;
        }

        public static string CreateTournament()
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);
                return string.Format("<a href=\"{0}\">Yarışmalar</a> \\ <b>Yarışma Oluştur</b>", urlHelper.Action("Tournaments"));
            }
        }

        public static string EditTournament(int? id)
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                var tournament = (from table in dbContext.Tournaments where table.Id == id select table).SingleOrDefault();

                var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);
                return string.Format("<a href=\"{0}\"><PERSON>r<PERSON><PERSON>malar</a> \\ <b>Yarışma Düzenle ({1})</b>", urlHelper.Action("Tournaments"), tournament.Name);
            }
        }

        public static string DeleteTournament(int? id)
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                var tournament = (from table in dbContext.Tournaments where table.Id == id select table).SingleOrDefault();

                var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);
                return string.Format("<a href=\"{0}\">Yarışmalar</a> \\ <b>Yarışma Silme ({1})</b>", urlHelper.Action("Tournaments"), tournament.Name);
            }
        }

        public static string TournamentGroups(int? id)
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                var tournament = (from table in dbContext.Tournaments where table.Id == id select table).SingleOrDefault();

                var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);
                return string.Format("<a href=\"{0}\">Yarışmalar</a> \\ {1} \\ <b>Yarışma Grupları</b>", urlHelper.Action("Tournaments"), tournament.Name);
            }
        }

        public static string CreateTournamentGroup(int? id)
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                var tournament = (from table in dbContext.Tournaments where table.Id == id select table).SingleOrDefault();

                var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);
                return string.Format("<a href=\"{0}\">Yarışmalar</a> \\ {1} \\ <b>Yarışma Grubu Oluştur</b>", urlHelper.Action("Tournaments"), tournament.Name);
            }
        }

        public static string EditTournamentGroup(int? id)
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                var tournamentGroup = (from table in dbContext.TournamentGroups where table.Id == id select table).SingleOrDefault();
                var tournament = (from table in dbContext.Tournaments where table.Id == id select table).SingleOrDefault();

                var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);
                return string.Format("<a href=\"{0}\">Yarışmalar</a> \\ {1} \\ <b>Yarışma Grubu Düzenle</b>", urlHelper.Action("Tournaments"), tournament.Name);
            }
        }

        public static string DeleteTournamentGroup(int? id)
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                var tournamentGroup = (from table in dbContext.TournamentGroups where table.Id == id select table).SingleOrDefault();
                var tournament = (from table in dbContext.Tournaments where table.Id == id select table).SingleOrDefault();

                var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);
                return string.Format("<a href=\"{0}\">Yarışmalar</a> \\ {1} \\ <b>Yarışma Grubu Sil</b>", urlHelper.Action("Tournaments"), tournament.Name);
            }
        }

        public static string TournamentGroupAthletes(int? id)
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                var tournamentGroup = (from table in dbContext.TournamentGroups where table.Id == id select table).SingleOrDefault();
                var tournament = (from table in dbContext.Tournaments where table.Id == tournamentGroup.TournamentId select table).SingleOrDefault();
                
                var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);
                return string.Format("<a href=\"{0}\">Yarışmalar</a> \\ {1} \\ <b>Yarışma Grubu Sporcuları</b>", urlHelper.Action("Tournaments"), tournament.Name);
            }
        }

        public static string TournamentReglament(int? id)
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                var tournamentGroup = (from table in dbContext.TournamentGroups where table.Id == id select table).SingleOrDefault();
                var tournament = (from table in dbContext.Tournaments where table.Id == id select table).SingleOrDefault();

                var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);
                return string.Format("<a href=\"{0}\">Yarışmalar</a> \\ {1} \\ <b>Yarışma Reglamanı</b>", urlHelper.Action("Tournaments"), tournament.Name);
            }
        }

        /*
        public static string EditProduct(int? id)
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);
                var product = (from table in dbContext.Products where table.Id == id select table).SingleOrDefault();
                return string.Format("<a href=\"{0}\">Products</a>&nbsp;\\ {1} \\&nbsp;<b>Edit Product</b>", urlHelper.Action("Products"), product.Name);
            }
        }

        public static string DeleteProduct(int? id)
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);
                var product = (from table in dbContext.Products where table.Id == id select table).SingleOrDefault();
                return string.Format("<a href=\"{0}\">Products</a>&nbsp;\\ {1} \\&nbsp;<b>Delete Product</b>", urlHelper.Action("Products"), product.Name);
            }
        }

        public static string ProductOrganizations(int? id)
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);
                var product = (from table in dbContext.Products where table.Id == id select table).SingleOrDefault();
                return string.Format("<a href=\"{0}\">Products</a>&nbsp;\\ {1} \\&nbsp;<b>Organizations</b>", urlHelper.Action("Products"), product.Name);
            }
        }

        public static string CreateProductOrganization(int? productId)
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);
                var product = (from table in dbContext.Products where table.Id == productId select table).SingleOrDefault();
                return string.Format("<a href=\"{0}\">Products</a>&nbsp;\\ {1} \\&nbsp;<b>Organizations</b>&nbsp;\\&nbsp;Create Organization", urlHelper.Action("Products"), product.Name);
            }
        }

        public static string EditProductOrganization(int? productId)
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);
                var product = (from table in dbContext.Products where table.Id == productId select table).SingleOrDefault();
                return string.Format("<a href=\"{0}\">Products</a>&nbsp;\\ {1} \\&nbsp;<b>Organizations</b>&nbsp;\\&nbsp;Edit Organization", urlHelper.Action("Products"), product.Name);
            }
        }

        public static string DeleteProductOrganization(int? productId)
        {
            using (var dbContext = new QLicenseEntities())
            {
                var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);
                var product = (from table in dbContext.Products where table.Id == productId select table).SingleOrDefault();
                return string.Format("<a href=\"{0}\">Products</a>&nbsp;\\ {1} \\&nbsp;<b>Organizations</b>&nbsp;\\&nbsp;Delete Organization", urlHelper.Action("Products"), product.Name);
            }
        }

        public static string ProductOrganizationLicensePacks(int? organizationId)
        {
            using (var dbContext = new QLicenseEntities())
            {
                var productOrganization = (from table in dbContext.ProductOrganizations where table.Id == organizationId select table).SingleOrDefault();
                var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);
                var product = (from table in dbContext.Products where table.Id == productOrganization.ProductId select table).SingleOrDefault();
                return string.Format("<a href=\"{0}\">Products</a>&nbsp;\\ {1} \\&nbsp;<a href=\"{2}\">Organizations</a>&nbsp;\\&nbsp;{3}&nbsp;\\&nbsp;<b>License Packs</b>", urlHelper.Action("Products"), product.Name, urlHelper.Action("ProductOrganizations", new { id = product.Id }), productOrganization.Name);
            }
        }

        public static string ProductReports(int? productId)
        {
            using (var dbContext = new QLicenseEntities())
            {
                var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);
                var product = (from table in dbContext.Products where table.Id == productId select table).SingleOrDefault();
                return string.Format("<a href=\"{0}\">Products</a>&nbsp;\\ {1} \\&nbsp;<b>Reports</b>", urlHelper.Action("Products"), product.Name);
            }
        }
        */
    }
}