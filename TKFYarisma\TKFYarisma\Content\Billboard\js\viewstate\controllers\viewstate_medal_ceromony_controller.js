function ViewStateMedalCeromonyController() {
    var self = this;

    this.viewStateName = "medal_ceromony";

    this.initialize = function (properties, first) {
        var $viewState = ElementHelper.getElementById($("body"), self.viewStateName);
        
        var medalCount = ViewStateHelper.getProperty(properties, "medal_count");

        var $goldMedal = $(ElementHelper.getElementById($viewState, "gold_medal"));
        var $goldMedalOrder = $(ElementHelper.getElementById($viewState, "gold_medal_order"));
        if (medalCount < 3) {
            /*
            $goldMedal.css("display", "none");
            $goldMedalOrder.css("display", "none");
            */
           $goldMedal.removeClass().addClass("animated fadeOutLeft");
           $goldMedalOrder.removeClass().addClass("animated fadeOutDown");
        } else {
            $goldMedal.css("display", "block");
            $goldMedalOrder.css("display", "block");
        }

        var $silverMedal = $(ElementHelper.getElementById($viewState, "silver_medal"));
        var $silverMedalOrder = $(ElementHelper.getElementById($viewState, "silver_medal_order"));
        if (medalCount < 2) {
            // $silverMedal.css("display", "none");
            // $silverMedalOrder.css("display", "none");
            $silverMedal.removeClass().addClass("animated fadeOutLeft");
            $silverMedalOrder.removeClass().addClass("animated fadeOutDown");
        } else {
            $silverMedal.css("display", "block");
            $silverMedalOrder.css("display", "block");
        }
    }

    this.deinitialize = function () {

    }
}