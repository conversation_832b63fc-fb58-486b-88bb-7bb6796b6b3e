﻿function ViewStateRefereesController() {
    var self = this;

    this.viewStateName = "referees";

    this.getRefereeByType = function (referees, refereeType) {
        for (var i = 0; i < referees.length; i++) {
            if (referees[i].referee_type == refereeType)
                return referees[i];
        }

        return null;
    }

    this.getRefereeByTypeAndOrder = function (referees, refereeType, refereeOrder) {
        for (var i = 0; i < referees.length; i++) {
            if (referees[i].referee_type == refereeType &&
                referees[i].referee_order == refereeOrder)
                return referees[i];
        }

        return null;
    }

    this.initialize = function (properties) {
        var $viewstate = ElementHelper.getElementById($("body"), self.viewStateName);

        var referees = ViewStateHelper.getProperty(properties, "referees");

        // president
        var referee = self.getRefereeByType(referees, "president");
        var $referee = $(ElementHelper.getElementById($viewstate, "president"));
        var $type = $(ElementHelper.getElementById($referee, "type"));
        $type.html("Federasyon Başkanı");
        var $photo = $(ElementHelper.getElementById($referee, "photo"));
        var $name = $(ElementHelper.getElementById($referee, "name"));
        if (referee != null) {
            $photo.attr("src", referee.referee_photo != null ? referee.referee_photo : "");
            $name.html(referee.referee_name != null ? referee.referee_name : "");
        } else {
            $photo.attr("src", "");
            $name.html("");
        }

        // chairperson
        var referee = self.getRefereeByType(referees, "chairperson");
        var $referee = $(ElementHelper.getElementById($viewstate, "chairperson"));
        var $type = $(ElementHelper.getElementById($referee, "type"));
        $type.html("Tertip Kurulu Başkanı");
        var $photo = $(ElementHelper.getElementById($referee, "photo"));
        var $name = $(ElementHelper.getElementById($referee, "name"));
        if (referee != null) {
            $photo.attr("src", referee.referee_photo != null ? referee.referee_photo : "");
            $name.html(referee.referee_name != null ? referee.referee_name : "");
        } else {
            $photo.attr("src", "");
            $name.html("");
        }

        // observer
        var referee = self.getRefereeByType(referees, "observer");
        var $referee = $(ElementHelper.getElementById($viewstate, "observer"));
        var $type = $(ElementHelper.getElementById($referee, "type"));
        $type.html("Gözlemci");
        var $photo = $(ElementHelper.getElementById($referee, "photo"));
        var $name = $(ElementHelper.getElementById($referee, "name"));
        if (referee != null) {
            $photo.attr("src", referee.referee_photo != null ? referee.referee_photo : "");
            $name.html(referee.referee_name != null ? referee.referee_name : "");
        } else {
            $photo.attr("src", "");
            $name.html("");
        }

        var referee = self.getRefereeByType(referees, "chief");
        var $referee = $(ElementHelper.getElementById($viewstate, "chief_referee"));
        var $type = $(ElementHelper.getElementById($referee, "type"));
        $type.html("Başhakem");
        var $photo = $(ElementHelper.getElementById($referee, "photo"));
        var $name = $(ElementHelper.getElementById($referee, "name"));
        if (referee != null) {
            $photo.attr("src", referee.referee_photo != null ? referee.referee_photo : "");
            $name.html(referee.referee_name != null ? referee.referee_name : "");
        } else {
            $photo.attr("src", "");
            $name.html("");
        }

        referee = self.getRefereeByType(referees, "secretary");
        $referee = $(ElementHelper.getElementById($viewstate, "secretary_referee"));
        $type = $(ElementHelper.getElementById($referee, "type"));
        $type.html("Sekreterya Hakemi");
        $photo = $(ElementHelper.getElementById($referee, "photo"));
        $name = $(ElementHelper.getElementById($referee, "name"));
        if (referee != null) {
            $photo.attr("src", referee.referee_photo != null ? referee.referee_photo : "");
            $name.html(referee.referee_name != null ? referee.referee_name : "");
        } else {
            $photo.attr("src", "");
            $name.html("");
        }

        referee = self.getRefereeByType(referees, "timing");
        $referee = $(ElementHelper.getElementById($viewstate, "timing_referee"));
        $type = $(ElementHelper.getElementById($referee, "type"));
        $type.html("Start Hakemi");
        $photo = $(ElementHelper.getElementById($referee, "photo"));
        $name = $(ElementHelper.getElementById($referee, "name"));
        if (referee != null) {
            $photo.attr("src", referee.referee_photo != null ? referee.referee_photo : "");
            $name.html(referee.referee_name != null ? referee.referee_name : "");
        } else {
            $photo.attr("src", "");
            $name.html("");
        }

        referee = self.getRefereeByTypeAndOrder(referees, "scoring", 0);
        $referee = $(ElementHelper.getElementById($viewstate, "scoring_referee1"));
        $type = $(ElementHelper.getElementById($referee, "type"));
        $type.html("Puanlama Hakemleri");
        $photo = $(ElementHelper.getElementById($referee, "photo"));
        $name = $(ElementHelper.getElementById($referee, "name"));
        if (referee != null) {
            $photo.attr("src", referee.referee_photo != null ? referee.referee_photo : "");
            $name.html(referee.referee_name != null ? referee.referee_name : "");
        } else {
            $photo.attr("src", "");
            $name.html("");
        }

        referee = self.getRefereeByTypeAndOrder(referees, "scoring", 1);
        $referee = $(ElementHelper.getElementById($viewstate, "scoring_referee2"));
        $photo = $(ElementHelper.getElementById($referee, "photo"));
        $name = $(ElementHelper.getElementById($referee, "name"));
        if (referee != null) {
            $photo.attr("src", referee.referee_photo != null ? referee.referee_photo : "");
            $name.html(referee.referee_name != null ? referee.referee_name : "");
        } else {
            $photo.attr("src", "");
            $name.html("");
        }

        referee = self.getRefereeByTypeAndOrder(referees, "scoring", 2);
        $referee = $(ElementHelper.getElementById($viewstate, "scoring_referee3"));
        $photo = $(ElementHelper.getElementById($referee, "photo"));
        $name = $(ElementHelper.getElementById($referee, "name"));
        if (referee != null) {
            $photo.attr("src", referee.referee_photo != null ? referee.referee_photo : "");
            $name.html(referee.referee_name != null ? referee.referee_name : "");
        } else {
            $photo.attr("src", "");
            $name.html("");
        }

        referee = self.getRefereeByTypeAndOrder(referees, "scoring", 3);
        $referee = $(ElementHelper.getElementById($viewstate, "scoring_referee4"));
        $photo = $(ElementHelper.getElementById($referee, "photo"));
        $name = $(ElementHelper.getElementById($referee, "name"));
        if (referee != null) {
            $photo.attr("src", referee.referee_photo != null ? referee.referee_photo : "");
            $name.html(referee.referee_name != null ? referee.referee_name : "");
        } else {
            $photo.attr("src", "");
            $name.html("");
        }

        referee = self.getRefereeByTypeAndOrder(referees, "scoring", 4);
        $referee = $(ElementHelper.getElementById($viewstate, "scoring_referee5"));
        $photo = $(ElementHelper.getElementById($referee, "photo"));
        $name = $(ElementHelper.getElementById($referee, "name"));
        if (referee != null) {
            $photo.attr("src", referee.referee_photo != null ? referee.referee_photo : "");
            $name.html(referee.referee_name != null ? referee.referee_name : "");
        } else {
            $photo.attr("src", "");
            $name.html("");
        }
    }

    this.deinitialize = function () {
        
    }
}