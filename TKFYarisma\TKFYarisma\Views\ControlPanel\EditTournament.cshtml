﻿@using TKFYarisma.Helpers

@model TKFYarisma.Models.Tournament

@{
    ViewBag.Title = "Yarışma Düzenle";
    ViewBag.Section = "Tournaments";
}

<div class="row">
    <div class="col">
        <div class="tile-header tile-header-big">
            <div class="row">
                <div class="col mr-auto">
                    <h3>Yarışma Düzenle</h3>
                </div>
            </div>
        </div>
        <div class="tile">
            @using (Html.BeginForm("EditTournament", "ControlPanel", FormMethod.Post))
            {
                <div class="row">
                    <div class="col">
                        <div class="row row-form">
                            <div class="col-form-label-medium">
                                <span class="align-middle">Yarışma Adı</span>
                            </div>
                            <div class="col">
                                @Html.TextBoxFor(d => d.Name, new { @class = "form-control", autofocus = "autofocus" })
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col">
                        <div class="row row-form-footer">
                            <input class="btn btn-primary" type="submit" value="Kaydet" />
                            <a href="javascript: history.back();" class="btn btn-secondary ml-2">İptal</a>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>