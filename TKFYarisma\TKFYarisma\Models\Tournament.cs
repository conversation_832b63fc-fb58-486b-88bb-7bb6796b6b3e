//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace TKFYarisma.Models
{
    using System;
    using System.Collections.Generic;
    
    public partial class Tournament
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public Nullable<int> ScoringInterval { get; set; }
        public Nullable<bool> Selected { get; set; }
        public Nullable<int> Style { get; set; }
        public string DisplayNameSuffix { get; set; }
        public Nullable<int> Tour { get; set; }
        public Nullable<System.DateTime> StartDate { get; set; }
        public Nullable<System.DateTime> EndDate { get; set; }
        public Nullable<int> CityId { get; set; }
        public string PresidentName { get; set; }
        public string PresidentPhotoFileName { get; set; }
        public string ChairpersonType { get; set; }
        public string ChairpersonName { get; set; }
        public string ChairpersonPhotoFileName { get; set; }
        public Nullable<int> ChairpersonRefereeId { get; set; }
        public string ObserverType { get; set; }
        public string ObserverName { get; set; }
        public string ObserverPhotoFileName { get; set; }
        public Nullable<int> ObserverRefereeId { get; set; }
        public Nullable<int> ChiefRefereeId { get; set; }
        public Nullable<int> SecretaryRefereeId { get; set; }
        public Nullable<bool> BestTrick { get; set; }
        public Nullable<int> BestTrickPointCount { get; set; }
    }
}
