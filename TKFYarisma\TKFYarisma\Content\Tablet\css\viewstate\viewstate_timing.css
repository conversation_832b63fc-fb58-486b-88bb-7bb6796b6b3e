﻿#timing {
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: "Roboto Condensed", sans-serif;
    color: #f0F0F0;
    text-shadow: 2px 1px #000000;
}

    #timing #background {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: -1;
    }

    #timing #container {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        flex-wrap: nowrap;
        left: auto;
        width: 80%;
        height: 600px;
        text-align: center;
        z-index: 1;
    }

        #timing #container #title #logo {
            margin: auto;
            width: 70%;
            display: block;
        }

        #timing #container #title #text {
            font-size: 36pt;
        }

    #timing #container #information {
        margin-top: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        font-size: 24pt;
    }

        #timing #container #information #referee {
            font-size: 20pt;
            margin-top: 10px;
        }

        #timing #container #information #athlete {
            font-size: 20pt;
            margin-top: 5px;
        }

        #timing #container #buttonStart {
            margin-top: 30px;
            width: 300px;
            height: 65px;
            font-size: 30pt;
            background-color: #28a745;
            border: none;
            border-radius: 10px;
            padding: 10px;
            color: #efefef;
            box-shadow: 0px 0px 15px #5a5a5a;
        }