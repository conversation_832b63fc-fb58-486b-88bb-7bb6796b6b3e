﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
    <Schema Namespace="TKFYarismaModel.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2012" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityType Name="Athletes">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="FirstName" Type="nvarchar" MaxLength="50" />
          <Property Name="LastName" Type="nvarchar" MaxLength="50" />
          <Property Name="Gender" Type="int" />
          <Property Name="BirthDate" Type="date" />
          <Property Name="DistrictId" Type="int" />
          <Property Name="CityId" Type="int" />
          <Property Name="LicenseNumber" Type="nvarchar" MaxLength="50" />
          <Property Name="PhotoFilename" Type="varchar" MaxLength="255" />
          <Property Name="Sign" Type="int" />
        </EntityType>
        <EntityType Name="Billboard">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ViewState" Type="varchar" MaxLength="30" />
          <Property Name="TimerStarted" Type="bit" />
          <Property Name="TimerFinished" Type="bit" />
          <Property Name="MedalCount" Type="int" />
        </EntityType>
        <EntityType Name="ControlCenter">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="TournamentId" Type="int" />
        </EntityType>
        <EntityType Name="DistrictCities">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="DistrictId" Type="int" />
          <Property Name="Name" Type="nvarchar" MaxLength="50" />
          <Property Name="PlateNumber" Type="int" />
        </EntityType>
        <EntityType Name="Districts">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="50" />
        </EntityType>
        <EntityType Name="Referees">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="LicenseNumber" Type="varchar" MaxLength="30" />
          <Property Name="FirstName" Type="nvarchar" MaxLength="50" />
          <Property Name="LastName" Type="nvarchar" MaxLength="50" />
          <Property Name="Gender" Type="int" />
          <Property Name="PhotoFilename" Type="varchar" MaxLength="255" />
          <Property Name="CityId" Type="int" />
        </EntityType>
        <EntityType Name="Tablets">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="255" />
          <Property Name="DisplayName" Type="nvarchar" MaxLength="255" />
          <Property Name="IpAddress" Type="varchar(max)" />
        </EntityType>
        <EntityType Name="TournamentDays">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="TournamentId" Type="int" />
          <Property Name="Date" Type="date" />
        </EntityType>
        <EntityType Name="TournamentGroupAthleteRefereeScores">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="GroupId" Type="int" />
          <Property Name="AthleteId" Type="int" />
          <Property Name="RefereeId" Type="int" />
          <Property Name="PhaseId" Type="int" />
          <Property Name="Tour" Type="int" />
          <Property Name="Score" Type="float" />
        </EntityType>
        <EntityType Name="TournamentGroupAthletes">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="GroupId" Type="int" />
          <Property Name="PhaseId" Type="int" />
          <Property Name="AthleteId" Type="int" />
          <Property Name="AthleteOrder" Type="int" />
          <Property Name="Selected" Type="bit" />
          <Property Name="Queued" Type="bit" />
        </EntityType>
        <EntityType Name="TournamentGroupAthleteScores">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="PhaseId" Type="int" />
          <Property Name="GroupId" Type="int" />
          <Property Name="Tour" Type="int" />
          <Property Name="AthleteId" Type="int" />
          <Property Name="Score" Type="float" />
        </EntityType>
        <EntityType Name="TournamentGroupMedals">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="GroupId" Type="int" />
          <Property Name="BronzeMedalAthleteId" Type="int" />
          <Property Name="SilverMedalAthleteId" Type="int" />
          <Property Name="GoldMedalAthleteId" Type="int" />
        </EntityType>
        <EntityType Name="TournamentGroupReferees">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="GroupId" Type="int" />
          <Property Name="PhaseId" Type="int" />
          <Property Name="RefereeId" Type="int" />
          <Property Name="RefereeType" Type="int" />
          <Property Name="RefereeOrder" Type="int" />
          <Property Name="TabletName" Type="varchar" MaxLength="30" />
        </EntityType>
        <EntityType Name="TournamentGroups">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="TournamentId" Type="int" />
          <Property Name="Name" Type="nvarchar" MaxLength="50" />
          <Property Name="RefereeDecidedFinalTourAthleteCount" Type="int" />
          <Property Name="Selected" Type="bit" />
          <Property Name="GroupOrder" Type="int" />
        </EntityType>
        <EntityType Name="TournamentGroupTeamAthletes">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="TeamId" Type="int" />
          <Property Name="AthleteId" Type="int" />
          <Property Name="Leader" Type="bit" />
        </EntityType>
        <EntityType Name="TournamentGroupTeams">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="GroupId" Type="int" />
          <Property Name="Name" Type="nvarchar" MaxLength="50" />
          <Property Name="ImageFileName" Type="varchar" MaxLength="255" />
        </EntityType>
        <EntityType Name="TournamentPhases">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="TournamentId" Type="int" />
          <Property Name="Name" Type="nvarchar" MaxLength="30" />
          <Property Name="StartPhase" Type="bit" />
          <Property Name="Selected" Type="bit" />
          <Property Name="PhaseOrder" Type="int" />
          <Property Name="EndPhase" Type="bit" />
          <Property Name="TourCount" Type="int" />
          <Property Name="SelectedTour" Type="int" />
        </EntityType>
        <EntityType Name="TournamentReferees">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="TournamentId" Type="int" />
          <Property Name="RefereeId" Type="int" />
          <Property Name="RefereeType" Type="int" />
        </EntityType>
        <EntityType Name="Tournaments">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="50" />
          <Property Name="ScoringInterval" Type="int" />
          <Property Name="Selected" Type="bit" />
          <Property Name="Style" Type="int" />
          <Property Name="DisplayNameSuffix" Type="nvarchar" MaxLength="255" />
          <Property Name="Tour" Type="int" />
          <Property Name="StartDate" Type="date" />
          <Property Name="EndDate" Type="date" />
          <Property Name="CityId" Type="int" />
          <Property Name="PresidentName" Type="nvarchar" MaxLength="255" />
          <Property Name="PresidentPhotoFileName" Type="varchar" MaxLength="255" />
          <Property Name="ChairpersonType" Type="varchar" MaxLength="30" />
          <Property Name="ChairpersonName" Type="nvarchar" MaxLength="255" />
          <Property Name="ChairpersonPhotoFileName" Type="varchar" MaxLength="255" />
          <Property Name="ChairpersonRefereeId" Type="int" />
          <Property Name="ObserverType" Type="varchar" MaxLength="30" />
          <Property Name="ObserverName" Type="nvarchar" MaxLength="255" />
          <Property Name="ObserverPhotoFileName" Type="varchar" MaxLength="255" />
          <Property Name="ObserverRefereeId" Type="int" />
          <Property Name="ChiefRefereeId" Type="int" />
          <Property Name="SecretaryRefereeId" Type="int" />
          <Property Name="BestTrick" Type="bit" />
          <Property Name="BestTrickPointCount" Type="int" />
        </EntityType>
        <EntityType Name="TournamentVideos">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="TournamentId" Type="int" />
          <Property Name="Name" Type="nvarchar" MaxLength="255" />
          <Property Name="FileName" Type="varchar" MaxLength="255" />
          <Property Name="Selected" Type="bit" />
        </EntityType>
        <EntityType Name="Users">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Username" Type="nvarchar" MaxLength="50" />
          <Property Name="Password" Type="nvarchar" MaxLength="50" />
          <Property Name="Enabled" Type="bit" />
          <Property Name="FirstName" Type="nvarchar" MaxLength="50" />
          <Property Name="LastName" Type="nvarchar" MaxLength="50" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'TKFYarisma.dbo.ViewAthletes' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="ViewAthletes">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" Nullable="false" />
          <Property Name="FirstName" Type="nvarchar" MaxLength="50" />
          <Property Name="LastName" Type="nvarchar" MaxLength="50" />
          <Property Name="Gender" Type="int" />
          <Property Name="BirthDate" Type="date" />
          <Property Name="LicenseNumber" Type="nvarchar" MaxLength="50" />
          <Property Name="PhotoFilename" Type="varchar" MaxLength="255" />
          <Property Name="DistrictId" Type="int" />
          <Property Name="DistrictName" Type="nvarchar" MaxLength="50" />
          <Property Name="CityId" Type="int" />
          <Property Name="CityName" Type="nvarchar" MaxLength="50" />
          <Property Name="Sign" Type="int" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'TKFYarisma.dbo.ViewReferees' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="ViewReferees">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" Nullable="false" />
          <Property Name="LicenseNumber" Type="varchar" MaxLength="30" />
          <Property Name="FirstName" Type="nvarchar" MaxLength="50" />
          <Property Name="LastName" Type="nvarchar" MaxLength="50" />
          <Property Name="Gender" Type="int" />
          <Property Name="CityId" Type="int" />
          <Property Name="CityName" Type="nvarchar" MaxLength="50" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'TKFYarisma.dbo.ViewTournamentGroupAthletes' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="ViewTournamentGroupAthletes">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" Nullable="false" />
          <Property Name="AthleteId" Type="int" />
          <Property Name="Selected" Type="bit" />
          <Property Name="PhaseId" Type="int" />
          <Property Name="FirstName" Type="nvarchar" MaxLength="50" />
          <Property Name="LastName" Type="nvarchar" MaxLength="50" />
          <Property Name="Gender" Type="int" />
          <Property Name="PhotoFilename" Type="varchar" MaxLength="255" />
          <Property Name="GroupId" Type="int" />
          <Property Name="GroupName" Type="nvarchar" MaxLength="50" />
          <Property Name="TournamentId" Type="int" />
          <Property Name="TournamentName" Type="nvarchar" MaxLength="50" />
          <Property Name="DistrictId" Type="int" />
          <Property Name="DistrictName" Type="nvarchar" MaxLength="50" />
          <Property Name="CityId" Type="int" />
          <Property Name="CityName" Type="nvarchar" MaxLength="50" />
          <Property Name="AthleteOrder" Type="int" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'TKFYarisma.dbo.ViewTournamentGroupReferees' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="ViewTournamentGroupReferees">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" Nullable="false" />
          <Property Name="RefereeId" Type="int" />
          <Property Name="PhaseId" Type="int" />
          <Property Name="FirstName" Type="nvarchar" MaxLength="50" />
          <Property Name="LastName" Type="nvarchar" MaxLength="50" />
          <Property Name="Gender" Type="int" />
          <Property Name="PhotoFilename" Type="varchar" MaxLength="255" />
          <Property Name="GroupId" Type="int" />
          <Property Name="GroupName" Type="nvarchar" MaxLength="50" />
          <Property Name="TournamentId" Type="int" />
          <Property Name="TournamentName" Type="nvarchar" MaxLength="50" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'TKFYarisma.dbo.ViewTournamentReferees' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="ViewTournamentReferees">
          <Key>
            <PropertyRef Name="Id" />
            <PropertyRef Name="RefereeName" />
          </Key>
          <Property Name="Id" Type="int" Nullable="false" />
          <Property Name="TournamentId" Type="int" />
          <Property Name="RefereeId" Type="int" />
          <Property Name="RefereeName" Type="nvarchar" MaxLength="101" Nullable="false" />
          <Property Name="RefereeType" Type="int" />
          <Property Name="RefereeCityId" Type="int" />
          <Property Name="RefereeCityName" Type="nvarchar" MaxLength="50" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'TKFYarisma.dbo.ViewTournaments' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="ViewTournaments">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="int" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="50" />
          <Property Name="Style" Type="int" />
          <Property Name="StartDate" Type="date" />
          <Property Name="EndDate" Type="date" />
          <Property Name="CityId" Type="int" />
          <Property Name="CityName" Type="nvarchar" MaxLength="50" />
        </EntityType>
        <Function Name="ProcedureGetAthleteScores" Aggregate="false" BuiltIn="false" NiladicFunction="false" IsComposable="false" ParameterTypeSemantics="AllowImplicitConversion" Schema="dbo">
          <Parameter Name="phaseId" Type="int" Mode="In" />
          <Parameter Name="groupId" Type="int" Mode="In" />
        </Function>
        <EntityContainer Name="TKFYarismaModelStoreContainer">
          <EntitySet Name="Athletes" EntityType="Self.Athletes" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Billboard" EntityType="Self.Billboard" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ControlCenter" EntityType="Self.ControlCenter" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="DistrictCities" EntityType="Self.DistrictCities" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Districts" EntityType="Self.Districts" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Referees" EntityType="Self.Referees" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Tablets" EntityType="Self.Tablets" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="TournamentDays" EntityType="Self.TournamentDays" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="TournamentGroupAthleteRefereeScores" EntityType="Self.TournamentGroupAthleteRefereeScores" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="TournamentGroupAthletes" EntityType="Self.TournamentGroupAthletes" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="TournamentGroupAthleteScores" EntityType="Self.TournamentGroupAthleteScores" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="TournamentGroupMedals" EntityType="Self.TournamentGroupMedals" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="TournamentGroupReferees" EntityType="Self.TournamentGroupReferees" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="TournamentGroups" EntityType="Self.TournamentGroups" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="TournamentGroupTeamAthletes" EntityType="Self.TournamentGroupTeamAthletes" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="TournamentGroupTeams" EntityType="Self.TournamentGroupTeams" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="TournamentPhases" EntityType="Self.TournamentPhases" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="TournamentReferees" EntityType="Self.TournamentReferees" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Tournaments" EntityType="Self.Tournaments" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="TournamentVideos" EntityType="Self.TournamentVideos" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Users" EntityType="Self.Users" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="ViewAthletes" EntityType="Self.ViewAthletes" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [ViewAthletes].[Id] AS [Id], 
    [ViewAthletes].[FirstName] AS [FirstName], 
    [ViewAthletes].[LastName] AS [LastName], 
    [ViewAthletes].[Gender] AS [Gender], 
    [ViewAthletes].[BirthDate] AS [BirthDate], 
    [ViewAthletes].[LicenseNumber] AS [LicenseNumber], 
    [ViewAthletes].[PhotoFilename] AS [PhotoFilename], 
    [ViewAthletes].[DistrictId] AS [DistrictId], 
    [ViewAthletes].[DistrictName] AS [DistrictName], 
    [ViewAthletes].[CityId] AS [CityId], 
    [ViewAthletes].[CityName] AS [CityName], 
    [ViewAthletes].[Sign] AS [Sign]
    FROM [dbo].[ViewAthletes] AS [ViewAthletes]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="ViewReferees" EntityType="Self.ViewReferees" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [ViewReferees].[Id] AS [Id], 
    [ViewReferees].[LicenseNumber] AS [LicenseNumber], 
    [ViewReferees].[FirstName] AS [FirstName], 
    [ViewReferees].[LastName] AS [LastName], 
    [ViewReferees].[Gender] AS [Gender], 
    [ViewReferees].[CityId] AS [CityId], 
    [ViewReferees].[CityName] AS [CityName]
    FROM [dbo].[ViewReferees] AS [ViewReferees]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="ViewTournamentGroupAthletes" EntityType="Self.ViewTournamentGroupAthletes" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [ViewTournamentGroupAthletes].[Id] AS [Id], 
    [ViewTournamentGroupAthletes].[AthleteId] AS [AthleteId], 
    [ViewTournamentGroupAthletes].[Selected] AS [Selected], 
    [ViewTournamentGroupAthletes].[PhaseId] AS [PhaseId], 
    [ViewTournamentGroupAthletes].[FirstName] AS [FirstName], 
    [ViewTournamentGroupAthletes].[LastName] AS [LastName], 
    [ViewTournamentGroupAthletes].[Gender] AS [Gender], 
    [ViewTournamentGroupAthletes].[PhotoFilename] AS [PhotoFilename], 
    [ViewTournamentGroupAthletes].[GroupId] AS [GroupId], 
    [ViewTournamentGroupAthletes].[GroupName] AS [GroupName], 
    [ViewTournamentGroupAthletes].[TournamentId] AS [TournamentId], 
    [ViewTournamentGroupAthletes].[TournamentName] AS [TournamentName], 
    [ViewTournamentGroupAthletes].[DistrictId] AS [DistrictId], 
    [ViewTournamentGroupAthletes].[DistrictName] AS [DistrictName], 
    [ViewTournamentGroupAthletes].[CityId] AS [CityId], 
    [ViewTournamentGroupAthletes].[CityName] AS [CityName], 
    [ViewTournamentGroupAthletes].[AthleteOrder] AS [AthleteOrder]
    FROM [dbo].[ViewTournamentGroupAthletes] AS [ViewTournamentGroupAthletes]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="ViewTournamentGroupReferees" EntityType="Self.ViewTournamentGroupReferees" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [ViewTournamentGroupReferees].[Id] AS [Id], 
    [ViewTournamentGroupReferees].[RefereeId] AS [RefereeId], 
    [ViewTournamentGroupReferees].[PhaseId] AS [PhaseId], 
    [ViewTournamentGroupReferees].[FirstName] AS [FirstName], 
    [ViewTournamentGroupReferees].[LastName] AS [LastName], 
    [ViewTournamentGroupReferees].[Gender] AS [Gender], 
    [ViewTournamentGroupReferees].[PhotoFilename] AS [PhotoFilename], 
    [ViewTournamentGroupReferees].[GroupId] AS [GroupId], 
    [ViewTournamentGroupReferees].[GroupName] AS [GroupName], 
    [ViewTournamentGroupReferees].[TournamentId] AS [TournamentId], 
    [ViewTournamentGroupReferees].[TournamentName] AS [TournamentName]
    FROM [dbo].[ViewTournamentGroupReferees] AS [ViewTournamentGroupReferees]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="ViewTournamentReferees" EntityType="Self.ViewTournamentReferees" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [ViewTournamentReferees].[Id] AS [Id], 
    [ViewTournamentReferees].[TournamentId] AS [TournamentId], 
    [ViewTournamentReferees].[RefereeId] AS [RefereeId], 
    [ViewTournamentReferees].[RefereeName] AS [RefereeName], 
    [ViewTournamentReferees].[RefereeType] AS [RefereeType], 
    [ViewTournamentReferees].[RefereeCityId] AS [RefereeCityId], 
    [ViewTournamentReferees].[RefereeCityName] AS [RefereeCityName]
    FROM [dbo].[ViewTournamentReferees] AS [ViewTournamentReferees]</DefiningQuery>
          </EntitySet>
          <EntitySet Name="ViewTournaments" EntityType="Self.ViewTournaments" store:Type="Views" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [ViewTournaments].[Id] AS [Id], 
    [ViewTournaments].[Name] AS [Name], 
    [ViewTournaments].[Style] AS [Style], 
    [ViewTournaments].[StartDate] AS [StartDate], 
    [ViewTournaments].[EndDate] AS [EndDate], 
    [ViewTournaments].[CityId] AS [CityId], 
    [ViewTournaments].[CityName] AS [CityName]
    FROM [dbo].[ViewTournaments] AS [ViewTournaments]</DefiningQuery>
          </EntitySet>
        </EntityContainer>
      </Schema></edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="TKFYarismaModel" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityContainer Name="TKFYarismaEntities" annotation:LazyLoadingEnabled="true">
          <FunctionImport Name="ProcedureGetAthleteScores" ReturnType="Collection(TKFYarismaModel.ProcedureGetAthleteScores_Result)">
            <Parameter Name="phaseId" Mode="In" Type="Int32" />
            <Parameter Name="groupId" Mode="In" Type="Int32" />
          </FunctionImport>
          <EntitySet Name="Athletes" EntityType="TKFYarismaModel.Athlete" />
          <EntitySet Name="Billboards" EntityType="TKFYarismaModel.Billboard" />
          <EntitySet Name="ControlCenters" EntityType="TKFYarismaModel.ControlCenter" />
          <EntitySet Name="DistrictCities" EntityType="TKFYarismaModel.DistrictCity" />
          <EntitySet Name="Districts" EntityType="TKFYarismaModel.District" />
          <EntitySet Name="Referees" EntityType="TKFYarismaModel.Referee" />
          <EntitySet Name="Tablets" EntityType="TKFYarismaModel.Tablet" />
          <EntitySet Name="TournamentDays" EntityType="TKFYarismaModel.TournamentDay" />
          <EntitySet Name="TournamentGroupAthleteRefereeScores" EntityType="TKFYarismaModel.TournamentGroupAthleteRefereeScore" />
          <EntitySet Name="TournamentGroupAthletes" EntityType="TKFYarismaModel.TournamentGroupAthlete" />
          <EntitySet Name="TournamentGroupAthleteScores" EntityType="TKFYarismaModel.TournamentGroupAthleteScore" />
          <EntitySet Name="TournamentGroupMedals" EntityType="TKFYarismaModel.TournamentGroupMedal" />
          <EntitySet Name="TournamentGroupReferees" EntityType="TKFYarismaModel.TournamentGroupReferee" />
          <EntitySet Name="TournamentGroups" EntityType="TKFYarismaModel.TournamentGroup" />
          <EntitySet Name="TournamentGroupTeamAthletes" EntityType="TKFYarismaModel.TournamentGroupTeamAthlete" />
          <EntitySet Name="TournamentGroupTeams" EntityType="TKFYarismaModel.TournamentGroupTeam" />
          <EntitySet Name="TournamentPhases" EntityType="TKFYarismaModel.TournamentPhas" />
          <EntitySet Name="TournamentReferees" EntityType="TKFYarismaModel.TournamentReferee" />
          <EntitySet Name="Tournaments" EntityType="TKFYarismaModel.Tournament" />
          <EntitySet Name="TournamentVideos" EntityType="TKFYarismaModel.TournamentVideo" />
          <EntitySet Name="Users" EntityType="TKFYarismaModel.User" />
          <EntitySet Name="ViewAthletes" EntityType="TKFYarismaModel.ViewAthlete" />
          <EntitySet Name="ViewReferees" EntityType="TKFYarismaModel.ViewReferee" />
          <EntitySet Name="ViewTournamentGroupAthletes" EntityType="TKFYarismaModel.ViewTournamentGroupAthlete" />
          <EntitySet Name="ViewTournamentGroupReferees" EntityType="TKFYarismaModel.ViewTournamentGroupReferee" />
          <EntitySet Name="ViewTournamentReferees" EntityType="TKFYarismaModel.ViewTournamentReferee" />
          <EntitySet Name="ViewTournaments" EntityType="TKFYarismaModel.ViewTournament" />
          </EntityContainer>
        <ComplexType Name="ProcedureGetAthleteScores_Result">
          <Property Type="Int32" Name="Id" Nullable="false" />
          <Property Type="Int32" Name="AthleteId" Nullable="true" />
          <Property Type="String" Name="FirstName" Nullable="true" MaxLength="50" />
          <Property Type="String" Name="LastName" Nullable="true" MaxLength="50" />
          <Property Type="Int32" Name="DistrictId" Nullable="true" />
          <Property Type="Int32" Name="CityId" Nullable="true" />
          <Property Type="Double" Name="Score1" Nullable="false" />
          <Property Type="Double" Name="Score2" Nullable="false" />
          <Property Type="Double" Name="Score3" Nullable="false" />
          <Property Type="Double" Name="BestTrick1" Nullable="false" />
          <Property Type="Double" Name="BestTrick2" Nullable="false" />
          <Property Type="Double" Name="BestTrick3" Nullable="false" />
          <Property Type="Double" Name="BestTrick4" Nullable="false" />
          <Property Type="Double" Name="BestTrick5" Nullable="false" />
          <Property Type="Boolean" Name="Queued" Nullable="true" />
        </ComplexType>
        <EntityType Name="Athlete">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="FirstName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="LastName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Gender" Type="Int32" />
          <Property Name="BirthDate" Type="DateTime" Precision="0" />
          <Property Name="DistrictId" Type="Int32" />
          <Property Name="CityId" Type="Int32" />
          <Property Name="LicenseNumber" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="PhotoFilename" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="Sign" Type="Int32" />
        </EntityType>
        <EntityType Name="Billboard">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ViewState" Type="String" MaxLength="30" FixedLength="false" Unicode="false" />
          <Property Name="TimerStarted" Type="Boolean" />
          <Property Name="TimerFinished" Type="Boolean" />
          <Property Name="MedalCount" Type="Int32" />
        </EntityType>
        <EntityType Name="ControlCenter">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="TournamentId" Type="Int32" />
        </EntityType>
        <EntityType Name="DistrictCity">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="DistrictId" Type="Int32" />
          <Property Name="Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="PlateNumber" Type="Int32" />
        </EntityType>
        <EntityType Name="District">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="Referee">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="LicenseNumber" Type="String" MaxLength="30" FixedLength="false" Unicode="false" />
          <Property Name="FirstName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="LastName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Gender" Type="Int32" />
          <Property Name="PhotoFilename" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="CityId" Type="Int32" />
        </EntityType>
        <EntityType Name="Tablet">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Name" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="DisplayName" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="IpAddress" Type="String" MaxLength="Max" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="TournamentDay">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="TournamentId" Type="Int32" />
          <Property Name="Date" Type="DateTime" Precision="0" />
        </EntityType>
        <EntityType Name="TournamentGroupAthleteRefereeScore">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="GroupId" Type="Int32" />
          <Property Name="AthleteId" Type="Int32" />
          <Property Name="RefereeId" Type="Int32" />
          <Property Name="PhaseId" Type="Int32" />
          <Property Name="Tour" Type="Int32" />
          <Property Name="Score" Type="Double" />
        </EntityType>
        <EntityType Name="TournamentGroupAthlete">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="GroupId" Type="Int32" />
          <Property Name="PhaseId" Type="Int32" />
          <Property Name="AthleteId" Type="Int32" />
          <Property Name="AthleteOrder" Type="Int32" />
          <Property Name="Selected" Type="Boolean" />
          <Property Name="Queued" Type="Boolean" />
        </EntityType>
        <EntityType Name="TournamentGroupAthleteScore">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="PhaseId" Type="Int32" />
          <Property Name="GroupId" Type="Int32" />
          <Property Name="Tour" Type="Int32" />
          <Property Name="AthleteId" Type="Int32" />
          <Property Name="Score" Type="Double" />
        </EntityType>
        <EntityType Name="TournamentGroupMedal">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="GroupId" Type="Int32" />
          <Property Name="BronzeMedalAthleteId" Type="Int32" />
          <Property Name="SilverMedalAthleteId" Type="Int32" />
          <Property Name="GoldMedalAthleteId" Type="Int32" />
        </EntityType>
        <EntityType Name="TournamentGroupReferee">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="GroupId" Type="Int32" />
          <Property Name="PhaseId" Type="Int32" />
          <Property Name="RefereeId" Type="Int32" />
          <Property Name="RefereeType" Type="Int32" />
          <Property Name="RefereeOrder" Type="Int32" />
          <Property Name="TabletName" Type="String" MaxLength="30" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="TournamentGroup">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="TournamentId" Type="Int32" />
          <Property Name="Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="RefereeDecidedFinalTourAthleteCount" Type="Int32" />
          <Property Name="Selected" Type="Boolean" />
          <Property Name="GroupOrder" Type="Int32" />
        </EntityType>
        <EntityType Name="TournamentGroupTeamAthlete">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="TeamId" Type="Int32" />
          <Property Name="AthleteId" Type="Int32" />
          <Property Name="Leader" Type="Boolean" />
        </EntityType>
        <EntityType Name="TournamentGroupTeam">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="GroupId" Type="Int32" />
          <Property Name="Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="ImageFileName" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="TournamentPhas">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="TournamentId" Type="Int32" />
          <Property Name="Name" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="StartPhase" Type="Boolean" />
          <Property Name="Selected" Type="Boolean" />
          <Property Name="PhaseOrder" Type="Int32" />
          <Property Name="EndPhase" Type="Boolean" />
          <Property Name="TourCount" Type="Int32" />
          <Property Name="SelectedTour" Type="Int32" />
        </EntityType>
        <EntityType Name="TournamentReferee">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="TournamentId" Type="Int32" />
          <Property Name="RefereeId" Type="Int32" />
          <Property Name="RefereeType" Type="Int32" />
        </EntityType>
        <EntityType Name="Tournament">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="ScoringInterval" Type="Int32" />
          <Property Name="Selected" Type="Boolean" />
          <Property Name="Style" Type="Int32" />
          <Property Name="DisplayNameSuffix" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="Tour" Type="Int32" />
          <Property Name="StartDate" Type="DateTime" Precision="0" />
          <Property Name="EndDate" Type="DateTime" Precision="0" />
          <Property Name="CityId" Type="Int32" />
          <Property Name="PresidentName" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="PresidentPhotoFileName" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="ChairpersonType" Type="String" MaxLength="30" FixedLength="false" Unicode="false" />
          <Property Name="ChairpersonName" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="ChairpersonPhotoFileName" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="ChairpersonRefereeId" Type="Int32" />
          <Property Name="ObserverType" Type="String" MaxLength="30" FixedLength="false" Unicode="false" />
          <Property Name="ObserverName" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="ObserverPhotoFileName" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="ObserverRefereeId" Type="Int32" />
          <Property Name="ChiefRefereeId" Type="Int32" />
          <Property Name="SecretaryRefereeId" Type="Int32" />
          <Property Name="BestTrick" Type="Boolean" />
          <Property Name="BestTrickPointCount" Type="Int32" />
        </EntityType>
        <EntityType Name="TournamentVideo">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="TournamentId" Type="Int32" />
          <Property Name="Name" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="FileName" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="Selected" Type="Boolean" />
        </EntityType>
        <EntityType Name="User">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Username" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Password" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Enabled" Type="Boolean" />
          <Property Name="FirstName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="LastName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="ViewAthlete">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" />
          <Property Name="FirstName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="LastName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Gender" Type="Int32" />
          <Property Name="BirthDate" Type="DateTime" Precision="0" />
          <Property Name="LicenseNumber" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="PhotoFilename" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="DistrictId" Type="Int32" />
          <Property Name="DistrictName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="CityId" Type="Int32" />
          <Property Name="CityName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Sign" Type="Int32" />
        </EntityType>
        <EntityType Name="ViewReferee">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" />
          <Property Name="LicenseNumber" Type="String" MaxLength="30" FixedLength="false" Unicode="false" />
          <Property Name="FirstName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="LastName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Gender" Type="Int32" />
          <Property Name="CityId" Type="Int32" />
          <Property Name="CityName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="ViewTournamentGroupAthlete">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" />
          <Property Name="AthleteId" Type="Int32" />
          <Property Name="Selected" Type="Boolean" />
          <Property Name="PhaseId" Type="Int32" />
          <Property Name="FirstName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="LastName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Gender" Type="Int32" />
          <Property Name="PhotoFilename" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="GroupId" Type="Int32" />
          <Property Name="GroupName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="TournamentId" Type="Int32" />
          <Property Name="TournamentName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="DistrictId" Type="Int32" />
          <Property Name="DistrictName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="CityId" Type="Int32" />
          <Property Name="CityName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="AthleteOrder" Type="Int32" />
        </EntityType>
        <EntityType Name="ViewTournamentGroupReferee">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" />
          <Property Name="RefereeId" Type="Int32" />
          <Property Name="PhaseId" Type="Int32" />
          <Property Name="FirstName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="LastName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Gender" Type="Int32" />
          <Property Name="PhotoFilename" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="GroupId" Type="Int32" />
          <Property Name="GroupName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="TournamentId" Type="Int32" />
          <Property Name="TournamentName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="ViewTournamentReferee">
          <Key>
            <PropertyRef Name="Id" />
            <PropertyRef Name="RefereeName" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" />
          <Property Name="TournamentId" Type="Int32" />
          <Property Name="RefereeId" Type="Int32" />
          <Property Name="RefereeName" Type="String" Nullable="false" MaxLength="101" FixedLength="false" Unicode="true" />
          <Property Name="RefereeType" Type="Int32" />
          <Property Name="RefereeCityId" Type="Int32" />
          <Property Name="RefereeCityName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="ViewTournament">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" />
          <Property Name="Name" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="Style" Type="Int32" />
          <Property Name="StartDate" Type="DateTime" Precision="0" />
          <Property Name="EndDate" Type="DateTime" Precision="0" />
          <Property Name="CityId" Type="Int32" />
          <Property Name="CityName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
        </EntityType>
        </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="TKFYarismaModelStoreContainer" CdmEntityContainer="TKFYarismaEntities">
          <FunctionImportMapping FunctionImportName="ProcedureGetAthleteScores" FunctionName="TKFYarismaModel.Store.ProcedureGetAthleteScores">
            <ResultMapping>
              <ComplexTypeMapping TypeName="TKFYarismaModel.ProcedureGetAthleteScores_Result">
                <ScalarProperty Name="Id" ColumnName="Id" />
                <ScalarProperty Name="AthleteId" ColumnName="AthleteId" />
                <ScalarProperty Name="FirstName" ColumnName="FirstName" />
                <ScalarProperty Name="LastName" ColumnName="LastName" />
                <ScalarProperty Name="DistrictId" ColumnName="DistrictId" />
                <ScalarProperty Name="CityId" ColumnName="CityId" />
                <ScalarProperty Name="Score1" ColumnName="Score1" />
                <ScalarProperty Name="Score2" ColumnName="Score2" />
                <ScalarProperty Name="Score3" ColumnName="Score3" />
                <ScalarProperty Name="BestTrick1" ColumnName="BestTrick1" />
                <ScalarProperty Name="BestTrick2" ColumnName="BestTrick2" />
                <ScalarProperty Name="BestTrick3" ColumnName="BestTrick3" />
                <ScalarProperty Name="BestTrick4" ColumnName="BestTrick4" />
                <ScalarProperty Name="BestTrick5" ColumnName="BestTrick5" />
                <ScalarProperty Name="Queued" ColumnName="Queued" />
              </ComplexTypeMapping>
            </ResultMapping>
          </FunctionImportMapping>
          <EntitySetMapping Name="Athletes">
            <EntityTypeMapping TypeName="TKFYarismaModel.Athlete">
              <MappingFragment StoreEntitySet="Athletes">
                <ScalarProperty Name="Sign" ColumnName="Sign" />
                <ScalarProperty Name="PhotoFilename" ColumnName="PhotoFilename" />
                <ScalarProperty Name="LicenseNumber" ColumnName="LicenseNumber" />
                <ScalarProperty Name="CityId" ColumnName="CityId" />
                <ScalarProperty Name="DistrictId" ColumnName="DistrictId" />
                <ScalarProperty Name="BirthDate" ColumnName="BirthDate" />
                <ScalarProperty Name="Gender" ColumnName="Gender" />
                <ScalarProperty Name="LastName" ColumnName="LastName" />
                <ScalarProperty Name="FirstName" ColumnName="FirstName" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Billboards">
            <EntityTypeMapping TypeName="TKFYarismaModel.Billboard">
              <MappingFragment StoreEntitySet="Billboard">
                <ScalarProperty Name="MedalCount" ColumnName="MedalCount" />
                <ScalarProperty Name="TimerFinished" ColumnName="TimerFinished" />
                <ScalarProperty Name="TimerStarted" ColumnName="TimerStarted" />
                <ScalarProperty Name="ViewState" ColumnName="ViewState" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ControlCenters">
            <EntityTypeMapping TypeName="TKFYarismaModel.ControlCenter">
              <MappingFragment StoreEntitySet="ControlCenter">
                <ScalarProperty Name="TournamentId" ColumnName="TournamentId" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DistrictCities">
            <EntityTypeMapping TypeName="TKFYarismaModel.DistrictCity">
              <MappingFragment StoreEntitySet="DistrictCities">
                <ScalarProperty Name="PlateNumber" ColumnName="PlateNumber" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="DistrictId" ColumnName="DistrictId" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Districts">
            <EntityTypeMapping TypeName="TKFYarismaModel.District">
              <MappingFragment StoreEntitySet="Districts">
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Referees">
            <EntityTypeMapping TypeName="TKFYarismaModel.Referee">
              <MappingFragment StoreEntitySet="Referees">
                <ScalarProperty Name="CityId" ColumnName="CityId" />
                <ScalarProperty Name="PhotoFilename" ColumnName="PhotoFilename" />
                <ScalarProperty Name="Gender" ColumnName="Gender" />
                <ScalarProperty Name="LastName" ColumnName="LastName" />
                <ScalarProperty Name="FirstName" ColumnName="FirstName" />
                <ScalarProperty Name="LicenseNumber" ColumnName="LicenseNumber" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Tablets">
            <EntityTypeMapping TypeName="TKFYarismaModel.Tablet">
              <MappingFragment StoreEntitySet="Tablets">
                <ScalarProperty Name="IpAddress" ColumnName="IpAddress" />
                <ScalarProperty Name="DisplayName" ColumnName="DisplayName" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="TournamentDays">
            <EntityTypeMapping TypeName="TKFYarismaModel.TournamentDay">
              <MappingFragment StoreEntitySet="TournamentDays">
                <ScalarProperty Name="Date" ColumnName="Date" />
                <ScalarProperty Name="TournamentId" ColumnName="TournamentId" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="TournamentGroupAthleteRefereeScores">
            <EntityTypeMapping TypeName="TKFYarismaModel.TournamentGroupAthleteRefereeScore">
              <MappingFragment StoreEntitySet="TournamentGroupAthleteRefereeScores">
                <ScalarProperty Name="Score" ColumnName="Score" />
                <ScalarProperty Name="Tour" ColumnName="Tour" />
                <ScalarProperty Name="PhaseId" ColumnName="PhaseId" />
                <ScalarProperty Name="RefereeId" ColumnName="RefereeId" />
                <ScalarProperty Name="AthleteId" ColumnName="AthleteId" />
                <ScalarProperty Name="GroupId" ColumnName="GroupId" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="TournamentGroupAthletes">
            <EntityTypeMapping TypeName="TKFYarismaModel.TournamentGroupAthlete">
              <MappingFragment StoreEntitySet="TournamentGroupAthletes">
                <ScalarProperty Name="Queued" ColumnName="Queued" />
                <ScalarProperty Name="Selected" ColumnName="Selected" />
                <ScalarProperty Name="AthleteOrder" ColumnName="AthleteOrder" />
                <ScalarProperty Name="AthleteId" ColumnName="AthleteId" />
                <ScalarProperty Name="PhaseId" ColumnName="PhaseId" />
                <ScalarProperty Name="GroupId" ColumnName="GroupId" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="TournamentGroupAthleteScores">
            <EntityTypeMapping TypeName="TKFYarismaModel.TournamentGroupAthleteScore">
              <MappingFragment StoreEntitySet="TournamentGroupAthleteScores">
                <ScalarProperty Name="Score" ColumnName="Score" />
                <ScalarProperty Name="AthleteId" ColumnName="AthleteId" />
                <ScalarProperty Name="Tour" ColumnName="Tour" />
                <ScalarProperty Name="GroupId" ColumnName="GroupId" />
                <ScalarProperty Name="PhaseId" ColumnName="PhaseId" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="TournamentGroupMedals">
            <EntityTypeMapping TypeName="TKFYarismaModel.TournamentGroupMedal">
              <MappingFragment StoreEntitySet="TournamentGroupMedals">
                <ScalarProperty Name="GoldMedalAthleteId" ColumnName="GoldMedalAthleteId" />
                <ScalarProperty Name="SilverMedalAthleteId" ColumnName="SilverMedalAthleteId" />
                <ScalarProperty Name="BronzeMedalAthleteId" ColumnName="BronzeMedalAthleteId" />
                <ScalarProperty Name="GroupId" ColumnName="GroupId" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="TournamentGroupReferees">
            <EntityTypeMapping TypeName="TKFYarismaModel.TournamentGroupReferee">
              <MappingFragment StoreEntitySet="TournamentGroupReferees">
                <ScalarProperty Name="TabletName" ColumnName="TabletName" />
                <ScalarProperty Name="RefereeOrder" ColumnName="RefereeOrder" />
                <ScalarProperty Name="RefereeType" ColumnName="RefereeType" />
                <ScalarProperty Name="RefereeId" ColumnName="RefereeId" />
                <ScalarProperty Name="PhaseId" ColumnName="PhaseId" />
                <ScalarProperty Name="GroupId" ColumnName="GroupId" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="TournamentGroups">
            <EntityTypeMapping TypeName="TKFYarismaModel.TournamentGroup">
              <MappingFragment StoreEntitySet="TournamentGroups">
                <ScalarProperty Name="GroupOrder" ColumnName="GroupOrder" />
                <ScalarProperty Name="Selected" ColumnName="Selected" />
                <ScalarProperty Name="RefereeDecidedFinalTourAthleteCount" ColumnName="RefereeDecidedFinalTourAthleteCount" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="TournamentId" ColumnName="TournamentId" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="TournamentGroupTeamAthletes">
            <EntityTypeMapping TypeName="TKFYarismaModel.TournamentGroupTeamAthlete">
              <MappingFragment StoreEntitySet="TournamentGroupTeamAthletes">
                <ScalarProperty Name="Leader" ColumnName="Leader" />
                <ScalarProperty Name="AthleteId" ColumnName="AthleteId" />
                <ScalarProperty Name="TeamId" ColumnName="TeamId" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="TournamentGroupTeams">
            <EntityTypeMapping TypeName="TKFYarismaModel.TournamentGroupTeam">
              <MappingFragment StoreEntitySet="TournamentGroupTeams">
                <ScalarProperty Name="ImageFileName" ColumnName="ImageFileName" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="GroupId" ColumnName="GroupId" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="TournamentPhases">
            <EntityTypeMapping TypeName="TKFYarismaModel.TournamentPhas">
              <MappingFragment StoreEntitySet="TournamentPhases">
                <ScalarProperty Name="SelectedTour" ColumnName="SelectedTour" />
                <ScalarProperty Name="TourCount" ColumnName="TourCount" />
                <ScalarProperty Name="EndPhase" ColumnName="EndPhase" />
                <ScalarProperty Name="PhaseOrder" ColumnName="PhaseOrder" />
                <ScalarProperty Name="Selected" ColumnName="Selected" />
                <ScalarProperty Name="StartPhase" ColumnName="StartPhase" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="TournamentId" ColumnName="TournamentId" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="TournamentReferees">
            <EntityTypeMapping TypeName="TKFYarismaModel.TournamentReferee">
              <MappingFragment StoreEntitySet="TournamentReferees">
                <ScalarProperty Name="RefereeType" ColumnName="RefereeType" />
                <ScalarProperty Name="RefereeId" ColumnName="RefereeId" />
                <ScalarProperty Name="TournamentId" ColumnName="TournamentId" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Tournaments">
            <EntityTypeMapping TypeName="TKFYarismaModel.Tournament">
              <MappingFragment StoreEntitySet="Tournaments">
                <ScalarProperty Name="BestTrickPointCount" ColumnName="BestTrickPointCount" />
                <ScalarProperty Name="BestTrick" ColumnName="BestTrick" />
                <ScalarProperty Name="SecretaryRefereeId" ColumnName="SecretaryRefereeId" />
                <ScalarProperty Name="ChiefRefereeId" ColumnName="ChiefRefereeId" />
                <ScalarProperty Name="ObserverRefereeId" ColumnName="ObserverRefereeId" />
                <ScalarProperty Name="ObserverPhotoFileName" ColumnName="ObserverPhotoFileName" />
                <ScalarProperty Name="ObserverName" ColumnName="ObserverName" />
                <ScalarProperty Name="ObserverType" ColumnName="ObserverType" />
                <ScalarProperty Name="ChairpersonRefereeId" ColumnName="ChairpersonRefereeId" />
                <ScalarProperty Name="ChairpersonPhotoFileName" ColumnName="ChairpersonPhotoFileName" />
                <ScalarProperty Name="ChairpersonName" ColumnName="ChairpersonName" />
                <ScalarProperty Name="ChairpersonType" ColumnName="ChairpersonType" />
                <ScalarProperty Name="PresidentPhotoFileName" ColumnName="PresidentPhotoFileName" />
                <ScalarProperty Name="PresidentName" ColumnName="PresidentName" />
                <ScalarProperty Name="CityId" ColumnName="CityId" />
                <ScalarProperty Name="EndDate" ColumnName="EndDate" />
                <ScalarProperty Name="StartDate" ColumnName="StartDate" />
                <ScalarProperty Name="Tour" ColumnName="Tour" />
                <ScalarProperty Name="DisplayNameSuffix" ColumnName="DisplayNameSuffix" />
                <ScalarProperty Name="Style" ColumnName="Style" />
                <ScalarProperty Name="Selected" ColumnName="Selected" />
                <ScalarProperty Name="ScoringInterval" ColumnName="ScoringInterval" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="TournamentVideos">
            <EntityTypeMapping TypeName="TKFYarismaModel.TournamentVideo">
              <MappingFragment StoreEntitySet="TournamentVideos">
                <ScalarProperty Name="Selected" ColumnName="Selected" />
                <ScalarProperty Name="FileName" ColumnName="FileName" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="TournamentId" ColumnName="TournamentId" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Users">
            <EntityTypeMapping TypeName="TKFYarismaModel.User">
              <MappingFragment StoreEntitySet="Users">
                <ScalarProperty Name="LastName" ColumnName="LastName" />
                <ScalarProperty Name="FirstName" ColumnName="FirstName" />
                <ScalarProperty Name="Enabled" ColumnName="Enabled" />
                <ScalarProperty Name="Password" ColumnName="Password" />
                <ScalarProperty Name="Username" ColumnName="Username" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ViewAthletes">
            <EntityTypeMapping TypeName="TKFYarismaModel.ViewAthlete">
              <MappingFragment StoreEntitySet="ViewAthletes">
                <ScalarProperty Name="Sign" ColumnName="Sign" />
                <ScalarProperty Name="CityName" ColumnName="CityName" />
                <ScalarProperty Name="CityId" ColumnName="CityId" />
                <ScalarProperty Name="DistrictName" ColumnName="DistrictName" />
                <ScalarProperty Name="DistrictId" ColumnName="DistrictId" />
                <ScalarProperty Name="PhotoFilename" ColumnName="PhotoFilename" />
                <ScalarProperty Name="LicenseNumber" ColumnName="LicenseNumber" />
                <ScalarProperty Name="BirthDate" ColumnName="BirthDate" />
                <ScalarProperty Name="Gender" ColumnName="Gender" />
                <ScalarProperty Name="LastName" ColumnName="LastName" />
                <ScalarProperty Name="FirstName" ColumnName="FirstName" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ViewReferees">
            <EntityTypeMapping TypeName="TKFYarismaModel.ViewReferee">
              <MappingFragment StoreEntitySet="ViewReferees">
                <ScalarProperty Name="CityName" ColumnName="CityName" />
                <ScalarProperty Name="CityId" ColumnName="CityId" />
                <ScalarProperty Name="Gender" ColumnName="Gender" />
                <ScalarProperty Name="LastName" ColumnName="LastName" />
                <ScalarProperty Name="FirstName" ColumnName="FirstName" />
                <ScalarProperty Name="LicenseNumber" ColumnName="LicenseNumber" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ViewTournamentGroupAthletes">
            <EntityTypeMapping TypeName="TKFYarismaModel.ViewTournamentGroupAthlete">
              <MappingFragment StoreEntitySet="ViewTournamentGroupAthletes">
                <ScalarProperty Name="AthleteOrder" ColumnName="AthleteOrder" />
                <ScalarProperty Name="CityName" ColumnName="CityName" />
                <ScalarProperty Name="CityId" ColumnName="CityId" />
                <ScalarProperty Name="DistrictName" ColumnName="DistrictName" />
                <ScalarProperty Name="DistrictId" ColumnName="DistrictId" />
                <ScalarProperty Name="TournamentName" ColumnName="TournamentName" />
                <ScalarProperty Name="TournamentId" ColumnName="TournamentId" />
                <ScalarProperty Name="GroupName" ColumnName="GroupName" />
                <ScalarProperty Name="GroupId" ColumnName="GroupId" />
                <ScalarProperty Name="PhotoFilename" ColumnName="PhotoFilename" />
                <ScalarProperty Name="Gender" ColumnName="Gender" />
                <ScalarProperty Name="LastName" ColumnName="LastName" />
                <ScalarProperty Name="FirstName" ColumnName="FirstName" />
                <ScalarProperty Name="PhaseId" ColumnName="PhaseId" />
                <ScalarProperty Name="Selected" ColumnName="Selected" />
                <ScalarProperty Name="AthleteId" ColumnName="AthleteId" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ViewTournamentGroupReferees">
            <EntityTypeMapping TypeName="TKFYarismaModel.ViewTournamentGroupReferee">
              <MappingFragment StoreEntitySet="ViewTournamentGroupReferees">
                <ScalarProperty Name="TournamentName" ColumnName="TournamentName" />
                <ScalarProperty Name="TournamentId" ColumnName="TournamentId" />
                <ScalarProperty Name="GroupName" ColumnName="GroupName" />
                <ScalarProperty Name="GroupId" ColumnName="GroupId" />
                <ScalarProperty Name="PhotoFilename" ColumnName="PhotoFilename" />
                <ScalarProperty Name="Gender" ColumnName="Gender" />
                <ScalarProperty Name="LastName" ColumnName="LastName" />
                <ScalarProperty Name="FirstName" ColumnName="FirstName" />
                <ScalarProperty Name="PhaseId" ColumnName="PhaseId" />
                <ScalarProperty Name="RefereeId" ColumnName="RefereeId" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ViewTournamentReferees">
            <EntityTypeMapping TypeName="TKFYarismaModel.ViewTournamentReferee">
              <MappingFragment StoreEntitySet="ViewTournamentReferees">
                <ScalarProperty Name="RefereeCityName" ColumnName="RefereeCityName" />
                <ScalarProperty Name="RefereeCityId" ColumnName="RefereeCityId" />
                <ScalarProperty Name="RefereeType" ColumnName="RefereeType" />
                <ScalarProperty Name="RefereeName" ColumnName="RefereeName" />
                <ScalarProperty Name="RefereeId" ColumnName="RefereeId" />
                <ScalarProperty Name="TournamentId" ColumnName="TournamentId" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ViewTournaments">
            <EntityTypeMapping TypeName="TKFYarismaModel.ViewTournament">
              <MappingFragment StoreEntitySet="ViewTournaments">
                <ScalarProperty Name="CityName" ColumnName="CityName" />
                <ScalarProperty Name="CityId" ColumnName="CityId" />
                <ScalarProperty Name="EndDate" ColumnName="EndDate" />
                <ScalarProperty Name="StartDate" ColumnName="StartDate" />
                <ScalarProperty Name="Style" ColumnName="Style" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="Id" ColumnName="Id" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="true" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
        <DesignerProperty Name="UseLegacyProvider" Value="false" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="None" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>