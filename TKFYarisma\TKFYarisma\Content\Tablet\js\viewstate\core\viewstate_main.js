var currentViewStateName = "";
var $currentViewState = undefined;
var viewStateFirstInitialize = true;
var lastViewStateData = undefined;

var viewStateControllers = new Array();
viewStateControllers.push(new ViewStateDefaultController());
viewStateControllers.push(new ViewStateScoringController());
viewStateControllers.push(new ViewStateTimingController());

$(document).ready(function () {
    console.info("ip adress > " + window.ipAddress);
    refreshViewState();
});

function refreshViewState() {
    $.ajax({
        url: Preferences.SERVER_DATA_URL + "?ipAddress=" + window.ipAddress,
        timeout: Preferences.SERVER_DATA_TIMEOUT,
        async: true,
        success: function (result) {
            if (result != null && JSON.stringify(result) != lastViewStateData) {
                lastViewStateData = JSON.stringify(result);
                setViewState(result.viewState, result.viewData);
                console.info("viewstate changed > " + currentViewStateName);
            }
            refreshViewState();
        },
        error: function (request, status, error) {
            console.error("request failed!");
            refreshViewState();
        }
    });
}

function setViewState(viewStateName, properties) {
    if (currentViewStateName != "") {
        var viewStateController = ViewStateHelper.getViewStateController(viewStateControllers, currentViewStateName);
        if (viewStateController != undefined) {
            if (viewStateController.deinitialize != undefined)
                viewStateController.deinitialize();
        }
    }

    if ($currentViewState != undefined && currentViewStateName != viewStateName) {
        $currentViewState.removeClass();
        $currentViewState.addClass("animated fadeOut");
        $currentViewState.css("pointer-events", "none");

        var inTransitionElements = $currentViewState.find("[out-transition-class]");
        for (var i = 0; i < inTransitionElements.length; i++) {
            var $inTransitionElement = $(inTransitionElements[i]);
            $inTransitionElement.removeClass();
            $inTransitionElement.addClass("animated " + $inTransitionElement.attr("out-transition-class"));
        }
    }

    var viewStates = $("body").find("viewstate#" + viewStateName)
    if (viewStates.length > 0) {
        var $viewState = $(viewStates[0]);

        ViewStateHelper.applyBindingToElements($viewState, properties, "");

        var inTransitionElements = $viewState.find("[in-transition-class]");
        for (var i = 0; i < inTransitionElements.length; i++) {
            var $inTransitionElement = $(inTransitionElements[i]);
            $inTransitionElement.removeClass();
            $inTransitionElement.addClass("animated " + $inTransitionElement.attr("in-transition-class"));
        }

        var viewStateController = ViewStateHelper.getViewStateController(viewStateControllers, viewStateName);
        if (viewStateController != undefined) {
            if (viewStateController.initialize != undefined)
                viewStateController.initialize(properties);
        }

        if (currentViewStateName != viewStateName) {
            $viewState.removeClass();
            $viewState.addClass("animated fadeIn");
            $viewState.css("pointer-events", "auto");
        }

        $currentViewState = $viewState;
        currentViewStateName = viewStateName;
    } else
        console.error("viewState cannot be found! [" + viewStateName + "]");
}