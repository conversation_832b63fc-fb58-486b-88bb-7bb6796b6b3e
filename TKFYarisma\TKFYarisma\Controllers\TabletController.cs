﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using TKFYarisma.Filters;
using TKFYarisma.Helpers;
using TKFYarisma.Models;

namespace TKFYarisma.Controllers
{
    public class TabletController : Controller
    {
        public ActionResult Index()
        {
            return View();
        }

        public ActionResult Interface(string ipAddress)
        {
            return View(new TabletInterfaceViewModel
            {
                IPAddress = ipAddress
            });
        }

        [AllowCrossSite]
        public ActionResult Data(string ipAddress)
        {
            var clientIpAddress = Request.UserHostAddress;

            if (!string.IsNullOrWhiteSpace(ipAddress) && ipAddress != "undefined")
                clientIpAddress = ipAddress;

            using (var dbContext = new TKFYarismaEntities())
            {
                var tablet = (from table in dbContext.Tablets where table.IpAddress == clientIpAddress select table).SingleOrDefault();
                if (tablet != null)
                {
                    var tournament = (from table in dbContext.Tournaments where table.Selected == true select table).SingleOrDefault();
                    if (tournament != null)
                    {
                        var tournamentGroup = (from table in dbContext.TournamentGroups where table.TournamentId == tournament.Id && table.Selected == true select table).SingleOrDefault();
                        if (tournamentGroup != null)
                        {
                            var tournamentPhase = (from table in dbContext.TournamentPhases where table.TournamentId == tournament.Id && table.Selected == true select table).SingleOrDefault();
                            if (tournamentPhase != null)
                            {
                                var tabletRefereeInfo = TKFTabletHelper.GetTournamentGroupRefereeForTablet(tournamentPhase, tournamentGroup, tablet.Name);
                                if (tabletRefereeInfo != null)
                                {
                                    var tournamentGroupAthlete = (from table in dbContext.TournamentGroupAthletes
                                                                  where table.PhaseId == tournamentPhase.Id &&
                                                                        table.GroupId == tournamentGroup.Id &&
                                                                        table.Selected == true select table).SingleOrDefault();
                                    if (tournamentGroupAthlete != null)
                                    {
                                        var tournamentGroupAthleteScore = (from table in dbContext.TournamentGroupAthleteRefereeScores
                                                                           where table.GroupId == tournamentGroup.Id &&
                                                                                 table.PhaseId == tournamentPhase.Id &&
                                                                                 table.AthleteId == tournamentGroupAthlete.AthleteId &&
                                                                                 table.RefereeId == tabletRefereeInfo.RefereeId &&
                                                                                 table.Tour == tournament.Tour
                                                                           select table).SingleOrDefault();
                                        if (tournamentGroupAthleteScore == null)
                                        {
                                            var billboard = (from table in dbContext.Billboards select table).FirstOrDefault();
                                            if (billboard != null)
                                            {
                                                if (tabletRefereeInfo.RefereeType == RefereeType.Scoring && billboard.ViewState == "athlete_timer" && billboard.TimerFinished == true)
                                                    return TKFTabletHelper.GetViewStateForScoring(dbContext, tablet.Name);
                                                else if (tabletRefereeInfo.RefereeType == RefereeType.Timing && billboard.ViewState == "athlete_timer" && billboard.TimerFinished == false)
                                                    return TKFTabletHelper.GetViewStateForTiming(dbContext, tablet.Name);
                                                else
                                                    return TKFTabletHelper.GetViewStateDefault(dbContext);
                                            }
                                            else
                                                return TKFTabletHelper.GetViewStateDefault(dbContext);
                                        }
                                        else
                                            return TKFTabletHelper.GetViewStateDefault(dbContext);
                                    }
                                    else
                                        return TKFTabletHelper.GetViewStateDefault(dbContext);
                                }
                                else
                                    return TKFTabletHelper.GetViewStateDefault(dbContext);
                            }
                            else
                                return TKFTabletHelper.GetViewStateDefault(dbContext);
                        }
                        else
                            return TKFTabletHelper.GetViewStateDefault(dbContext);
                    }
                    else
                        return TKFTabletHelper.GetViewStateDefault(dbContext);
                }
                else
                    return TKFTabletHelper.GetViewStateDefault(dbContext);
            }
        }

        [HttpPost]
        [AllowCrossSite]
        public ActionResult Score(double value)
        {
            var clientIpAddress = Request.UserHostAddress;

            using (var dbContext = new TKFYarismaEntities())
            {
                var tournament = (from table in dbContext.Tournaments where table.Selected == true select table).SingleOrDefault();
                if (tournament != null)
                {
                    var tournamentPhase = (from table in dbContext.TournamentPhases where table.TournamentId == tournament.Id && table.Selected == true select table).SingleOrDefault();
                    if (tournamentPhase != null)
                    {
                        var tournamentGroup = (from table in dbContext.TournamentGroups where table.TournamentId == tournament.Id && table.Selected == true select table).SingleOrDefault();
                        if (tournamentGroup != null)
                        {
                            var tournamentGroupAthlete = (from table in dbContext.TournamentGroupAthletes where table.PhaseId == tournamentPhase.Id && table.GroupId == tournamentGroup.Id && table.Selected == true select table).SingleOrDefault();
                            if (tournamentGroupAthlete != null)
                            {
                                var tablet = (from table in dbContext.Tablets where table.IpAddress == clientIpAddress select table).SingleOrDefault();
                                if (tablet != null)
                                {
                                    var tableRefereeInfo = TKFTabletHelper.GetTournamentGroupRefereeForTablet(tournamentPhase, tournamentGroup, tablet.Name);
                                    if (tableRefereeInfo != null)
                                    {
                                        var tournamentGroupAthleteScore = (from table in dbContext.TournamentGroupAthleteRefereeScores
                                                                           where table.GroupId == tournamentGroup.Id &&
                                                                                 table.PhaseId == tournamentPhase.Id &&
                                                                                 table.AthleteId == tournamentGroupAthlete.AthleteId &&
                                                                                 table.RefereeId == tableRefereeInfo.RefereeId &&
                                                                                 table.Tour == tournament.Tour
                                                                           select table).SingleOrDefault();

                                        if (tournamentGroupAthleteScore == null)
                                        {
                                            dbContext.TournamentGroupAthleteRefereeScores.Add(new TournamentGroupAthleteRefereeScore()
                                            {
                                                GroupId = tournamentGroup.Id,
                                                PhaseId = tournamentPhase.Id,
                                                AthleteId = tournamentGroupAthlete.AthleteId,
                                                RefereeId = tableRefereeInfo.RefereeId,
                                                Tour = tournament.Tour,
                                                Score = value
                                            });
                                        }
                                        else
                                            tournamentGroupAthleteScore.Score = value;

                                        dbContext.SaveChanges();

                                        return Content("success");
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return Content("failed");
        }

        [HttpPost]
        [AllowCrossSite]
        public ActionResult Time()
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                var billboard = (from table in dbContext.Billboards select table).FirstOrDefault();
                if (billboard != null)
                {
                    billboard.TimerStarted = true;
                    dbContext.SaveChanges();

                    return Content("success");
                }
            }

            return Content("failed");
        }
    }
}