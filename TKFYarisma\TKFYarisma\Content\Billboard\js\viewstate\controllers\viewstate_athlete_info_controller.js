function ViewStateAthleteInfoController() {
    var self = this;
    var totalTime = 0;

    var timeoutId = undefined;
    var intervalId = undefined;

    var listItemCount = 0;
    var scrollerDirection = 1;
    var scrollerYOffset = 0;

    this.viewStateName = "athlete_info";

    this.initialize = function (properties) {
        var $viewState = ElementHelper.getElementById($("body"), self.viewStateName);

        // under age
        var $helmet = $(ElementHelper.getElementById($viewState, "helmet"));
        var underAge = ViewStateHelper.getProperty(properties, "under_age");
        if (underAge)
            $helmet.css("display", "block");
        else
            $helmet.css("display", "none");
    }

    this.deinitialize = function () {

    }
}