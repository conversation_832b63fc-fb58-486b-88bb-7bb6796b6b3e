function ViewStateTimingController() {
    var self = this;

    this.viewStateName = "timing";

    this.initialize = function (properties) {
        var $viewstate = ElementHelper.getElementById($("body"), self.viewStateName);

        var timerStarted = ViewStateHelper.getProperty(properties, "timer_started");
        var $buttonStart = $("#buttonStart");
        if (timerStarted)
            $buttonStart.css("display", "none");
        else
            $buttonStart.css("display", "block");

        $("#buttonStart").on("click", buttonStartPressed);
    }

    this.deinitialize = function () {
        $("#buttonStart").off("click", buttonStartPressed);
    }

    function buttonStartPressed(e) {
        e.preventDefault();

        var SERVER_URL = Preferences.SERVER_TIME_URL;
        var SERVER_TIMEOUT = Preferences.SERVER_TIME_TIMEOUT;

        $.ajax({
            url: SERVER_URL,
            timeout: SERVER_TIMEOUT,
            async: true,
            type: "POST",
            processData: false,
            success: function (result) {
                $(e.currentTarget).css("display", "none");
            },
            error: function (request, status, error) {
                console.info("time error");
            }
        });
    }
}