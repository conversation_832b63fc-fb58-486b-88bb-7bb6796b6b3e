﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Mvc;
using TKFYarisma.Models;

namespace TKFYarisma.Helpers
{
    public class TKFTabletHelper
    {
        public static Tournament GetSelectedTournament(TKFYarismaEntities dbContext)
        {
            return (from table in dbContext.Tournaments where table.Selected == true select table).SingleOrDefault();
        }

        public static TournamentPhas GetSelectedTournamentPhase(TKFYarismaEntities dbContext, Tournament tournament)
        {
            return (from table in dbContext.TournamentPhases where table.TournamentId == tournament.Id && table.Selected == true select table).SingleOrDefault();
        }

        public static Billboard GetBillboard(TKFYarismaEntities dbContext)
        {
            return (from table in dbContext.Billboards select table).FirstOrDefault();
        }

        public static TournamentGroupAthlete GetSelectedTournamentGroupAthlete(TKFYarismaEntities dbContext, TournamentPhas tournamentPhase, TournamentGroup tournamentGroup)
        {
            return (from table in dbContext.TournamentGroupAthletes where table.PhaseId == tournamentPhase.Id && table.GroupId == tournamentGroup.Id && table.Selected == true select table).SingleOrDefault();
        }

        public static Athlete GetAthlete(TKFYarismaEntities dbContext, TournamentGroupAthlete tournamentGroupAthlete)
        {
            return (from table in dbContext.Athletes where table.Id == tournamentGroupAthlete.AthleteId select table).SingleOrDefault();
        }

        public static Referee GetReferee(TKFYarismaEntities dbContext, int? id)
        {
            return (from table in dbContext.Referees where table.Id == id select table).SingleOrDefault();
        }

        public static TournamentGroup GetSelectedTournamentGroup(TKFYarismaEntities dbContext, Tournament tournament)
        {
            return (from table in dbContext.TournamentGroups where table.TournamentId == tournament.Id && table.Selected == true select table).SingleOrDefault();
        }

        private static string GetAthleteDistrictName(TKFYarismaEntities dbContext, Athlete athlete)
        {
            var district = (from table in dbContext.Districts
                            where table.Id == athlete.DistrictId
                            select table).SingleOrDefault();

            return district != null ? district.Name : string.Empty;
        }

        private static string GetAthleteDistrictName(TKFYarismaEntities dbContext, int? id)
        {
            var district = (from table in dbContext.Districts
                            where table.Id == id
                            select table).SingleOrDefault();

            return district != null ? district.Name : string.Empty;
        }

        private static string GetAthleteCityName(TKFYarismaEntities dbContext, Athlete athlete)
        {
            var city = (from table in dbContext.DistrictCities
                        where table.Id == athlete.CityId
                        select table).SingleOrDefault();

            return city != null ? city.Name : string.Empty;
        }

        private static string GetAthleteCityName(TKFYarismaEntities dbContext, int? id)
        {
            var city = (from table in dbContext.DistrictCities
                        where table.Id == id
                        select table).SingleOrDefault();

            return city != null ? city.Name : string.Empty;
        }

        private static string GetTournamentName(TKFYarismaEntities dbContext)
        {
            var result = string.Empty;

            var tournament = GetSelectedTournament(dbContext);
            if (tournament != null)
            {
                if (tournament.Style == 0)
                    result += "Sokak Disiplini";
                else if (tournament.Style == 1)
                    result += "Park Disiplini";
            }

            var tournamentGroup = GetSelectedTournamentGroup(dbContext, tournament);
            if (tournamentGroup != null)
                result += " " + tournamentGroup.Name;

            result += " " + tournament.DisplayNameSuffix;

            return result;
        }

        private static JArray SerializeDictionary(Dictionary<string, object> dictionary)
        {
            var properties = new JArray();

            foreach (var key in dictionary.Keys)
            {
                var property = new JObject
                {
                    ["name"] = key
                };

                if (dictionary[key] is string)
                    property["value"] = (string)dictionary[key];
                else if (dictionary[key] is int)
                    property["value"] = (int)dictionary[key];
                else if (dictionary[key] is long)
                    property["value"] = (long)dictionary[key];
                else if (dictionary[key] is double)
                    property["value"] = (double)dictionary[key];
                else if (dictionary[key] is bool)
                    property["value"] = (bool)dictionary[key];
                else if (dictionary[key] is ArrayList arrayList)
                {
                    var jArray = new JArray();

                    for (int i = 0; i < arrayList.Count; i++)
                    {
                        var dictionary_ = (Dictionary<string, object>)arrayList[i];
                        jArray.Add(SerializeDictionary(dictionary_));
                    }

                    property["value"] = jArray;
                }
                else if (dictionary[key] is JArray jArray)
                    property["value"] = jArray;

                properties.Add(property);
            }

            return properties;
        }

        public static ContentResult GetViewStateDefault(TKFYarismaEntities dbContext)
        {
            var billboard = GetBillboard(dbContext);

            return new ContentResult()
            {
                Content = new JObject()
                {
                    ["viewState"] = "default"
                }.ToString(),
                ContentType = "application/json",
                ContentEncoding = Encoding.UTF8
            };
        }

        public static TabletRefereeInfo GetTournamentGroupRefereeForTablet(TournamentPhas tournamentPhase, TournamentGroup tournamentGroup, string tabletName)
        {
            return GetTournamentGroupRefereeForTablet(tournamentPhase, tournamentGroup, 0, tabletName);
        }

        public static TabletRefereeInfo GetTournamentGroupRefereeForTablet(TournamentPhas tournamentPhase, TournamentGroup tournamentGroup, int refereeType, string tabletName)
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                var tournamentGroupReferee = (from table in dbContext.TournamentGroupReferees
                                              where table.GroupId == tournamentGroup.Id &&
                                                    table.PhaseId == tournamentPhase.Id &&
                                                    (refereeType == 0 || table.RefereeType == refereeType) &&
                                                    table.TabletName == tabletName
                                              select table).SingleOrDefault();
                if (tournamentGroupReferee != null)
                {
                    return new TabletRefereeInfo()
                    {
                        TabletName = tabletName,
                        RefereeId = tournamentGroupReferee.RefereeId != null ? tournamentGroupReferee.RefereeId.Value : 0,
                        RefereeType = tournamentGroupReferee.RefereeType != null ? tournamentGroupReferee.RefereeType.Value : 0,
                        RefereeOrder = tournamentGroupReferee.RefereeOrder != null ? tournamentGroupReferee.RefereeOrder.Value : 0,
                    };
                }
                else
                    return null;
            }
        }


        public static ContentResult GetViewStateForScoring(TKFYarismaEntities dbContext, string tabletName)
        {
            var tournament = GetSelectedTournament(dbContext);
            var tournamentGroup = GetSelectedTournamentGroup(dbContext, tournament);
            var tournamentPhase = GetSelectedTournamentPhase(dbContext, tournament);
            var tournamentGroupReferee = GetTournamentGroupRefereeForTablet(tournamentPhase, tournamentGroup, RefereeType.Scoring, tabletName);
            var referee = GetReferee(dbContext, tournamentGroupReferee.RefereeId);
            var tournamentGroupAthlete = GetSelectedTournamentGroupAthlete(dbContext, tournamentPhase, tournamentGroup);
            var athlete = GetAthlete(dbContext, tournamentGroupAthlete);

            var properties = new Dictionary<string, object>();
            properties.Add("tournament_name", GetTournamentName(dbContext));
            properties.Add("tournament_style", tournament.Style);
            properties.Add("tournament_tour", tournament.Tour);

            properties.Add("referee_name", string.Format("{0} {1}", referee.FirstName, referee.LastName));
            properties.Add("athlete_name", string.Format("{0} {1}", athlete.FirstName, athlete.LastName));

            properties.Add("end_phase", tournamentPhase.EndPhase == true);

            var scores = new JArray();

            var athleteScores = (from table in dbContext.ProcedureGetAthleteScores(tournamentPhase.Id, tournamentGroup.Id)
                                 select table).ToList();

            if (tournamentPhase.EndPhase != true)
            {
                var calculatedAthleteScores = (from table in athleteScores
                                               select new
                                               {
                                                   Id = table.Id,
                                                   AthleteId = table.AthleteId,
                                                   FirstName = table.FirstName,
                                                   LastName = table.LastName,
                                                   DistrictId = table.DistrictId,
                                                   CityId = table.CityId,
                                                   Score1 = table.Score1,
                                                   Score2 = table.Score2,
                                                   Score3 = table.Score3,
                                                   Score = ScoreHelper.GetHighestScore(table.Score1, table.Score2, table.Score3),
                                                   Queued = table.Queued
                                               }).ToList();

                var orderedAthleteScores = calculatedAthleteScores.OrderByDescending(d => d.Score).ToList();

                var index = 1;
                foreach (var athleteScore in orderedAthleteScores)
                {
                    var score = new JObject();

                    score["athlete_order"] = index;
                    var athleteName = string.Format("{0} {1}", athleteScore.FirstName, athleteScore.LastName);
                    if (athleteName.Length > 15)
                    {
                        if (!string.IsNullOrEmpty(athleteScore.FirstName))
                        {
                            var firstName = string.Empty;

                            var firstNameTokens = athleteScore.FirstName.Split(' ');
                            if (firstNameTokens.Length == 2)
                            {
                                firstName += firstNameTokens[0].Substring(0, 1) + ". " + firstNameTokens[1];
                            }
                            else if (firstNameTokens.Length > 2)
                            {
                                firstName += firstNameTokens[firstNameTokens.Length - 1];
                            }
                            else
                                firstName = athleteScore.FirstName;

                            athleteName = string.Format("{0} {1}", firstName, athleteScore.LastName);

                            //if (athleteName.Length > 15)
                            //    athleteName = athleteName.Substring(0, 15) + "...";
                        }
                    }
                    score["athlete_name"] = athleteName;

                    var districtName = GetAthleteDistrictName(dbContext, athleteScore.DistrictId);
                    score["athlete_district_name"] = districtName;
                    var cityName = GetAthleteCityName(dbContext, athleteScore.CityId);
                    score["athlete_city_name"] = cityName;

                    score["score_1"] = athleteScore.Score1;
                    score["score_2"] = athleteScore.Score2;
                    score["score_3"] = athleteScore.Score3;
                    score["is_queued"] = athleteScore.Queued == true;
                    score["score"] = athleteScore.Score;

                    scores.Add(score);

                    index++;
                }
            }
            else
            {
                var athleteScoreInfos = new List<AthleteScoreInfo>();

                foreach (var athleteScore in athleteScores)
                {
                    var highestScore = ScoreHelper.GetHighestScore(athleteScore.Score1, athleteScore.Score2, athleteScore.Score3);

                    var totalScore = highestScore;

                    if (tournament.BestTrick == true)
                    {
                        var orderedScores = new List<double>();

                        orderedScores.Add(athleteScore.BestTrick1);
                        orderedScores.Add(athleteScore.BestTrick2);
                        orderedScores.Add(athleteScore.BestTrick3);
                        orderedScores.Add(athleteScore.BestTrick4);
                        orderedScores.Add(athleteScore.BestTrick5);

                        orderedScores.Sort();

                        totalScore += ScoreHelper.GetBestTrickSummary(orderedScores, tournament.BestTrickPointCount);
                    }

                    athleteScoreInfos.Add(new AthleteScoreInfo()
                    {
                        AthleteId = athleteScore.AthleteId != null ? athleteScore.AthleteId.Value : 0,
                        FirstName = athleteScore.FirstName,
                        LastName = athleteScore.LastName,
                        CityId = athleteScore.CityId,
                        DistrictId = athleteScore.DistrictId,
                        Score1 = athleteScore.Score1,
                        Score2 = athleteScore.Score2,
                        Score3 = athleteScore.Score3,
                        BestTrick1 = athleteScore.BestTrick1,
                        BestTrick2 = athleteScore.BestTrick2,
                        BestTrick3 = athleteScore.BestTrick3,
                        BestTrick4 = athleteScore.BestTrick4,
                        BestTrick5 = athleteScore.BestTrick5,
                        TotalScore = totalScore,
                        Queued = athleteScore.Queued
                    });
                }

                var index = 1;
                foreach (var orderedAthleteScoreInfo in athleteScoreInfos.OrderByDescending(d => d.TotalScore))
                {
                    var score = new JObject();

                    score["athlete_order"] = index;
                    var athleteName = string.Format("{0} {1}", orderedAthleteScoreInfo.FirstName, orderedAthleteScoreInfo.LastName);
                    if (athleteName.Length > 15)
                    {
                        if (!string.IsNullOrEmpty(orderedAthleteScoreInfo.FirstName))
                        {
                            var firstName = string.Empty;

                            var firstNameTokens = orderedAthleteScoreInfo.FirstName.Split(' ');
                            if (firstNameTokens.Length == 2)
                            {
                                firstName += firstNameTokens[0].Substring(0, 1) + ". " + firstNameTokens[1];
                            }
                            else if (firstNameTokens.Length > 2)
                            {
                                firstName += firstNameTokens[firstNameTokens.Length - 1];
                            }
                            else
                                firstName = orderedAthleteScoreInfo.FirstName;

                            athleteName = string.Format("{0} {1}", firstName, orderedAthleteScoreInfo.LastName);

                            //if (athleteName.Length > 15)
                            //    athleteName = athleteName.Substring(0, 15) + "...";
                        }
                    }
                    score["athlete_name"] = athleteName;

                    var districtName = GetAthleteDistrictName(dbContext, orderedAthleteScoreInfo.DistrictId);
                    score["athlete_district_name"] = districtName;
                    var cityName = GetAthleteCityName(dbContext, orderedAthleteScoreInfo.CityId);
                    score["athlete_city_name"] = cityName;

                    score["score_1"] = orderedAthleteScoreInfo.Score1;
                    score["score_2"] = orderedAthleteScoreInfo.Score2;
                    score["score_3"] = orderedAthleteScoreInfo.Score3;
                    score["best_trick_1"] = orderedAthleteScoreInfo.BestTrick1;
                    score["best_trick_2"] = orderedAthleteScoreInfo.BestTrick2;
                    score["best_trick_3"] = orderedAthleteScoreInfo.BestTrick3;
                    score["best_trick_4"] = orderedAthleteScoreInfo.BestTrick4;
                    score["best_trick_5"] = orderedAthleteScoreInfo.BestTrick5;
                    score["total_score"] = orderedAthleteScoreInfo.TotalScore;
                    score["is_queued"] = orderedAthleteScoreInfo.Queued == true;

                    scores.Add(score);

                    index++;
                }
            }

            properties.Add("scores", scores);

            return new ContentResult()
            {
                Content = new JObject()
                {
                    ["viewState"] = "scoring",
                    ["viewData"] = SerializeDictionary(properties)
                }.ToString(),
                ContentType = "application/json",
                ContentEncoding = Encoding.UTF8
            };
        }

        public static ContentResult GetViewStateForTiming(TKFYarismaEntities dbContext, string tabletName)
        {
            var tournament = GetSelectedTournament(dbContext);
            var tournamentGroup = GetSelectedTournamentGroup(dbContext, tournament);
            var tournamentPhase = GetSelectedTournamentPhase(dbContext, tournament);
            var tournamentGroupReferee = GetTournamentGroupRefereeForTablet(tournamentPhase, tournamentGroup, RefereeType.Timing, tabletName);
            var referee = GetReferee(dbContext, tournamentGroupReferee.RefereeId);
            var tournamentGroupAthlete = GetSelectedTournamentGroupAthlete(dbContext, tournamentPhase, tournamentGroup);
            var athlete = GetAthlete(dbContext, tournamentGroupAthlete);

            var properties = new Dictionary<string, object>();
            properties.Add("tournament_name", GetTournamentName(dbContext));

            if (referee != null)
                properties.Add("referee_name", string.Format("{0} {1}", referee.FirstName, referee.LastName));

            if (athlete != null)
                properties.Add("athlete_name", string.Format("{0} {1}", athlete.FirstName, athlete.LastName));

            var billboard = GetBillboard(dbContext);
            properties.Add("timer_started", billboard.TimerStarted == true);

            return new ContentResult()
            {
                Content = new JObject()
                {
                    ["viewState"] = "timing",
                    ["viewData"] = SerializeDictionary(properties)
                }.ToString(),
                ContentType = "application/json",
                ContentEncoding = Encoding.UTF8
            };
        }
    }
}