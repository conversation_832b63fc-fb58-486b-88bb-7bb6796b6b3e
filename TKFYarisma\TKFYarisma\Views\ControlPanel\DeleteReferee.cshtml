﻿@using TKFYarisma.Helpers

@model TKFYarisma.Models.RefereeModel

@{
    ViewBag.Title = "Hakem Sil";
    ViewBag.Section = "Referees";
}

<div class="row">
    <div class="col">
        <div class="tile-header tile-header-big">
            <div class="row">
                <div class="col mr-auto">
                    <h3>Hakem Sil</h3>
                </div>
            </div>
        </div>
        <div class="tile">
            @using (Html.BeginForm("DeleteReferee", "ControlPanel", FormMethod.Post))
            {
                @Html.HiddenFor(d => d.Referee.Id)

                <div class="row">
                    <div class="col">
                        <div class="row row-form">
                            <div class="col-form-label-medium">
                                <span class="align-middle">Lisans No.</span>
                            </div>
                            <div class="col">
                                @Html.TextBoxFor(d => d.Referee.LicenseNumber, new { @class = "form-control", disabled = "disabled" })
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <div class="row row-form">
                            <div class="col-form-label-medium">
                                <span class="align-middle">Adı</span>
                            </div>
                            <div class="col">
                                @Html.TextBoxFor(d => d.Referee.FirstName, new { @class = "form-control", disabled = "disabled" })
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <div class="row row-form">
                            <div class="col-form-label-medium">
                                <span class="align-middle">Soyadı</span>
                            </div>
                            <div class="col">
                                @Html.TextBoxFor(d => d.Referee.LastName, new { @class = "form-control", disabled = "disabled" })
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <div class="row row-form">
                            <div class="col-form-label-medium">
                                <span class="align-middle">Cinsiyet</span>
                            </div>
                            <div class="col">
                                @{
                                    var items = new List<SelectListItem>();
                                    items.Add(new SelectListItem()
                                    {
                                        Text = string.Empty,
                                        Value = string.Empty,
                                        Selected = Model.Referee.Gender == null || Model.Referee.Gender == 0
                                    });
                                    items.Add(new SelectListItem()
                                    {
                                        Text = "Erkek",
                                        Value = "1",
                                        Selected = Model.Referee.Gender == 1
                                    });
                                    items.Add(new SelectListItem()
                                    {
                                        Text = "Kadın",
                                        Value = "2",
                                        Selected = Model.Referee.Gender == 2
                                    });
                                    var selectList = new SelectList(items, "Value", "Text", Model.Referee.Gender);
                                }
                                @Html.DropDownListFor(d => d.Referee.Gender, selectList, new { @class = "form-control", disabled = "disabled" })
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col">
                        <div class="row row-form">
                            <div class="col-form-label-medium">
                                <span class="align-middle">Bölge</span>
                            </div>
                            <div class="col">
                                @Html.DropDownListFor(d => d.Referee.DistrictId,
                                                      new SelectList(new List<SelectListItem>(), "value", "text", Model.Referee.DistrictId),
                                                      new { @class = "form-control", disabled = "disabled" })
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col">
                        <div class="row row-form">
                            <div class="col-form-label-medium">
                                <span class="align-middle">Şehir</span>
                            </div>
                            <div class="col">
                                @Html.DropDownListFor(d => d.Referee.CityId,
                                                      new SelectList(new List<SelectListItem>(), "value", "text", Model.Referee.CityId),
                                                      new { @class = "form-control", disabled = "disabled" })
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col">
                        <div class="row row-form-dynamic">
                            <div class="col-form-label-medium">
                                <span class="align-middle">Fotoğraf</span>
                            </div>
                            <div class="col">
                                <div class="row">
                                    <div class="col">@Model.Referee.PhotoFilename</div>
                                </div>
                                <div class="row">
                                    <div class="col">
                                        <img class="img-fluid row-image" src="@Url.Content("~/Uploads/Referee/Photo/" + Model.Referee.PhotoFilename)" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col">
                        <div class="row mt-3">
                            <div class="col">
                                <div class="row row-form-footer">
                                    <input class="btn btn-danger" type="submit" value="Sil" />
                                    <a href="javascript: history.back();" class="btn btn-secondary ml-2">İptal</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>

        <script type="text/javascript">
            $(function () {
                $('#BirthDateContainer').datetimepicker({
                    format: 'DD.MM.YYYY',
                    locale: 'tr'
                });
            });

            var districts = [];
            @foreach (var district in Model.Districts)
            {
                <text>districts.push({
                name: "@Html.Raw(district.Name)",
                value: "@Html.Raw(district.Id)"
            });</text>
            }

            var districtCities = [];
            @foreach (var districtCity in Model.DistrictCities)
            {
                <text>districtCities.push({
                districtId: "@Html.Raw(districtCity.DistrictId)",
                name: "@Html.Raw(districtCity.Name)",
                value: "@Html.Raw(districtCity.Id)"
            });</text>
            }

            // fill districts dropdown
            var $districtDropDown = $("#Referee_DistrictId");
            $districtDropDown.append($('<option>', {
                text: "",
                value: ""
            }));
            for (var i = 0; i < districts.length; i++) {
                var district = districts[i];
                $districtDropDown.append($('<option>', {
                    text: district.name,
                    value: district.value
                }));
            }

            var $districtCitiesDropDown = $("#Referee_CityId");

            function refreshCity(districtId) {
                $districtCitiesDropDown.empty();
                $districtCitiesDropDown.append($('<option>', {
                    text: "",
                    value: ""
                }));
                for (var i = 0; i < districtCities.length; i++) {
                    var districtCity = districtCities[i];
                    if (districtCity.districtId == districtId) {
                        $districtCitiesDropDown.append($('<option>', {
                            text: districtCity.name,
                            value: districtCity.value
                        }));
                    }
                }
            }

            // fill cities dropdown
            var $districtDropDown = $("#Referee_DistrictId");
            $districtDropDown.on("change", function (e) {
                var districtId = e.currentTarget.value;
                refreshCity(districtId);
            });

            @if (Model.Referee.DistrictId != null)
            {
            <text>
            $districtDropDown.val(@Model.Referee.DistrictId);
            refreshCity(@Model.Referee.DistrictId);
            $districtCitiesDropDown.val(@Model.Referee.CityId);
            </text>
            }
        </script>
    </div>
</div>