function ViewStateAthleteOrdersController() {
    var self = this;
    var totalTime = 0;

    var timeoutId = undefined;
    var intervalId = undefined;

    var listItemCount = 0;
    var scrollerDirection = 1;
    var scrollerYOffset = 0;

    this.viewStateName = "athlete_orders";

    this.initialize = function (properties) {
        var $viewState = ElementHelper.getElementById($("body"), self.viewStateName);

        var tour = ViewStateHelper.getProperty(properties, "tour");

        var $list = $(ElementHelper.getElementById($viewState, "list"));
        var $row = $(ElementHelper.getElementById($list, "row"));
        var $scroller = $(ElementHelper.getElementById($list, "scroller"));
        
        $scroller.html("");

        var athletes = ViewStateHelper.getProperty(properties, "athletes");
        if (athletes != undefined) {
            listItemCount = athletes.length;

            var rowIndex = 0;
            var rowYOffset = 30;
            for (var i = 0; i < athletes.length; i++) {
                var score = athletes[i];

                var $newRow = $row.clone();
                $scroller.append($newRow);
                
                $newRow.css("top", (rowYOffset + (rowIndex * 150)) + "px");
                $newRow.css("display", "block");

                var $rowAthleteOrder = $(ElementHelper.getElementById($newRow, "athlete_order"));
                $rowAthleteOrder.html(score.athlete_order);

                var $rowAthleteName = $(ElementHelper.getElementById($newRow, "athlete_name"));
                $rowAthleteName.html(score.athlete_name);

                var $rowAthleteCityName = $(ElementHelper.getElementById($newRow, "athlete_city_name"));
                $rowAthleteCityName.html(score.athlete_city_name);

                rowIndex++;
            }
        }

        scrollerDirection = 1;
        scrollerYOffset = 0;
        var $scroller = $(ElementHelper.getElementById($viewState, "scroller"));
        $scroller.css("top", scrollerYOffset);

        timeoutId = setTimeout(self.waitList, 3000);
    }

    this.waitList = function () {
        clearTimeout(timeoutId);
        intervalId = setInterval(self.scrollList, 10);
    }

    this.scrollList = function () {
        scrollerYOffset += scrollerDirection == 1 ? -2 : 2;

        var $viewstate = ElementHelper.getElementById($("body"), self.viewStateName);
        var $scroller = $(ElementHelper.getElementById($viewstate, "scroller"));
        $scroller.css("top", scrollerYOffset);

        if ((scrollerDirection == 1 && (scrollerYOffset < (-listItemCount * 150) + (5 * 150) - 50)) ||
            (scrollerDirection == 0 && scrollerYOffset > 0)) {
            clearInterval(intervalId);
            timeoutId = setTimeout(self.switchScrollerDirection, 3000);
        }
    }

    this.switchScrollerDirection = function () {
        clearTimeout(timeoutId);
        scrollerDirection = 1 - scrollerDirection;
        intervalId = setInterval(self.scrollList, 10);
    }

    this.deinitialize = function () {
        if (timeoutId != undefined)
            clearTimeout(timeoutId);

        if (intervalId != undefined)
            clearInterval(intervalId);
    }
}