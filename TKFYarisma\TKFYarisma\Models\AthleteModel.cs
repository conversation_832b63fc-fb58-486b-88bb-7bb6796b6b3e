﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace TKFYarisma.Models
{
    public class AthleteModel
    {
        public Athlete Athlete { get; set; }
        public IEnumerable<District> Districts { get; set; }
        public IEnumerable<DistrictCity> DistrictCities { get; set; }
    }

    public class AthleteInfo
    {
        public string PhotoUrl { get; set; }
        public string Name { get; set; }
        public string DistrictAndCity { get; set; }
    }

    public class AthleteScoreInfo
    {
        public int AthleteId { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public int? CityId { get; set; }
        public int? DistrictId { get; set; }
        public double Score1 { get; set; }
        public double Score2 { get; set; }
        public double Score3 { get; set; }
        public double BestTrick1 { get; set; }
        public double BestTrick2 { get; set; }
        public double BestTrick3 { get; set; }
        public double BestTrick4 { get; set; }
        public double BestTrick5 { get; set; }
        public double TotalScore { get; set; }
        public bool? Queued { get; set; }
    }
}