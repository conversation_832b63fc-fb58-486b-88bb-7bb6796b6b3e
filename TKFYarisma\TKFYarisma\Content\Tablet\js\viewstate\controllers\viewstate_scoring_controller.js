function ViewStateScoringController() {
    var self = this;
    var valueStr = "";

    this.viewStateName = "scoring";

    this.formatScore = function (score, tournamentStyle) {
        var scoreStr = score.toString();
        var dotIndex = scoreStr.indexOf(".");
        if (dotIndex != -1) {
            var scoreHighStr = scoreStr.substring(0, dotIndex);
            if (tournamentStyle == 1) {
                for (var i = 0; i < 2 - scoreHighStr.length; i++)
                    scoreHighStr = "0" + scoreHighStr;
            }

            var scoreLowStr = scoreStr.substring(dotIndex + 1, scoreStr.length);
            if (scoreLowStr.length < 2) {
                for (var i = 0; i < 2 - scoreLowStr.length; i++)
                    scoreLowStr = scoreLowStr + "0";
            }
            if (scoreLowStr.length > 2)
                scoreLowStr = scoreLowStr.substring(0, 2);
        } else {
            var scoreHighStr = scoreStr;
            if (tournamentStyle == 1) {
                for (var i = 0; i < 2 - scoreHighStr.length; i++)
                    scoreHighStr = "0" + scoreHighStr;
            }

            var scoreLowStr = "00";
        }

        return scoreHighStr + "." + scoreLowStr;
    }

    this.initialize = function (properties) {
        var $viewState = ElementHelper.getElementById($("body"), self.viewStateName);

        var tournamentStyle = ViewStateHelper.getProperty(properties, "tournament_style");
        var tour = ViewStateHelper.getProperty(properties, "tournament_tour");
        var endPhase = ViewStateHelper.getProperty(properties, "end_phase");

        var $row = $(ElementHelper.getElementById($viewState, "row"));
        var $list = $(ElementHelper.getElementById($viewState, "information-athlete-list"));

        var $bestTrick1Title = $(ElementHelper.getElementById($viewState, "best-trick-1-title"));
        var $bestTrick2Title = $(ElementHelper.getElementById($viewState, "best-trick-2-title"));
        var $bestTrick3Title = $(ElementHelper.getElementById($viewState, "best-trick-3-title"));
        var $bestTrick4Title = $(ElementHelper.getElementById($viewState, "best-trick-4-title"));
        var $bestTrick5Title = $(ElementHelper.getElementById($viewState, "best-trick-5-title"));

        $bestTrick1Title.css("display", endPhase ? "block" : "none");
        $bestTrick2Title.css("display", endPhase ? "block" : "none");
        $bestTrick3Title.css("display", endPhase ? "block" : "none");
        $bestTrick4Title.css("display", endPhase ? "block" : "none");
        $bestTrick5Title.css("display", endPhase ? "block" : "none");

        var scores = ViewStateHelper.getProperty(properties, "scores");
        console.info("scores > ", scores);
        if (scores != undefined) {
            $list.html("");

            var rowIndex = 0;
            var rowYOffset = 30;
            for (var i = 0; i < scores.length; i++) {
                var score = scores[i];

                var $newRow = $row.clone();
                $list.append($newRow);

                var $rowAthleteOrderText = $(ElementHelper.getElementById($newRow, "athlete-order"));
                $rowAthleteOrderText.html(score.athlete_order);

                var $rowAthleteName = $(ElementHelper.getElementById($newRow, "athlete-name"));
                $rowAthleteName.html(score.athlete_name);

                var score1 = score.score_1;
                var score2 = score.score_2;
                var score3 = score.score_3;

                var bestTrick1 = score.bestTrick1 != undefined ? score.bestTrick1 : 0;
                var bestTrick2 = score.bestTrick2 != undefined ? score.bestTrick2 : 0;
                var bestTrick3 = score.bestTrick3 != undefined ? score.bestTrick3 : 0;
                var bestTrick4 = score.bestTrick4 != undefined ? score.bestTrick4 : 0;
                var bestTrick5 = score.bestTrick5 != undefined ? score.bestTrick5 : 0;

                var $score1Title = $(ElementHelper.getElementById($newRow, "score-1-title"));
                var $score2Title = $(ElementHelper.getElementById($newRow, "score-2-title"));
                var $score3Title = $(ElementHelper.getElementById($newRow, "score-3-title"));

                var $score1 = $(ElementHelper.getElementById($newRow, "score-1"));
                var $score2 = $(ElementHelper.getElementById($newRow, "score-2"));
                var $score3 = $(ElementHelper.getElementById($newRow, "score-3"));

                var $bestTrick1 = $(ElementHelper.getElementById($newRow, "best-trick-1"));
                var $bestTrick2 = $(ElementHelper.getElementById($newRow, "best-trick-2"));
                var $bestTrick3 = $(ElementHelper.getElementById($newRow, "best-trick-3"));
                var $bestTrick4 = $(ElementHelper.getElementById($newRow, "best-trick-4"));
                var $bestTrick5 = $(ElementHelper.getElementById($newRow, "best-trick-5"));

                $score1.text(this.formatScore(score1, tournamentStyle));
                $score2.text(this.formatScore(score2, tournamentStyle));
                $score3.text(this.formatScore(score3, tournamentStyle));

                $bestTrick1.text(this.formatScore(bestTrick1, tournamentStyle));
                $bestTrick2.text(this.formatScore(bestTrick2, tournamentStyle));
                $bestTrick3.text(this.formatScore(bestTrick3, tournamentStyle));
                $bestTrick4.text(this.formatScore(bestTrick4, tournamentStyle));
                $bestTrick5.text(this.formatScore(bestTrick5, tournamentStyle));

                $bestTrick1.css("display", endPhase ? "block" : "none");
                $bestTrick2.css("display", endPhase ? "block" : "none");
                $bestTrick3.css("display", endPhase ? "block" : "none");
                $bestTrick4.css("display", endPhase ? "block" : "none");
                $bestTrick5.css("display", endPhase ? "block" : "none");

                if (tour == 1) {
                    // hide tour 2 column
                    var $score2Title = $(ElementHelper.getElementById($viewState, "score_2_title"));
                    $score2Title.css("display", "none");
                    var $score2 = $(ElementHelper.getElementById($newRow, "score_2"));
                    $score2.css("display", "none");

                    var $score3Title = $(ElementHelper.getElementById($viewState, "score_3_title"));
                    $score3Title.css("display", "none");
                    var $score3 = $(ElementHelper.getElementById($newRow, "score_3"));
                    $score3.css("display", "none");
                } else if (tour == 2) {
                    // hide tour 2 column
                    var $score2Title = $(ElementHelper.getElementById($viewState, "score_2_title"));
                    $score2Title.css("display", "block");
                    var $score2 = $(ElementHelper.getElementById($newRow, "score_2"));
                    $score2.css("display", "block");

                    var $score3Title = $(ElementHelper.getElementById($viewState, "score_3_title"));
                    $score3Title.css("display", "none");
                    var $score3 = $(ElementHelper.getElementById($newRow, "score_3"));
                    $score3.css("display", "none");
                } else if (tour == 3) {
                    var $score3Title = $(ElementHelper.getElementById($viewState, "score_3_title"));
                    $score3Title.css("display", "block");
                    var $score3 = $(ElementHelper.getElementById($newRow, "score_3"));
                    $score3.css("display", "block");

                    var $score2Title = $(ElementHelper.getElementById($viewState, "score_2_title"));
                    $score2Title.css("display", "block");
                    var $score2 = $(ElementHelper.getElementById($newRow, "score_2"));
                    $score2.css("display", "block");
                }

                rowIndex++;
            }
        }

        if (tournamentStyle == 0)
            $("#keypad_decimal_point").html(",");
        else
            $("#keypad_decimal_point").html("&nbsp;");

        $("#keypad_0").on("click", keypad_number_pressed);
        $("#keypad_1").on("click", keypad_number_pressed);
        $("#keypad_2").on("click", keypad_number_pressed);
        $("#keypad_3").on("click", keypad_number_pressed);
        $("#keypad_4").on("click", keypad_number_pressed);
        $("#keypad_5").on("click", keypad_number_pressed);
        $("#keypad_6").on("click", keypad_number_pressed);
        $("#keypad_7").on("click", keypad_number_pressed);
        $("#keypad_8").on("click", keypad_number_pressed);
        $("#keypad_9").on("click", keypad_number_pressed);
        $("#keypad_delete").on("click", keypad_delete_pressed);
        $("#keypad_submit").on("click", keypad_submit_pressed);
        $("#keypad_decimal_point").on("click", keypad_decimal_point_pressed);

        valueStr = "";
        self.refreshDisplay();
    }

    this.deinitialize = function () {
        $("#keypad_0").off("click", keypad_number_pressed);
        $("#keypad_1").off("click", keypad_number_pressed);
        $("#keypad_2").off("click", keypad_number_pressed);
        $("#keypad_3").off("click", keypad_number_pressed);
        $("#keypad_4").off("click", keypad_number_pressed);
        $("#keypad_5").off("click", keypad_number_pressed);
        $("#keypad_6").off("click", keypad_number_pressed);
        $("#keypad_7").off("click", keypad_number_pressed);
        $("#keypad_8").off("click", keypad_number_pressed);
        $("#keypad_9").off("click", keypad_number_pressed);
        $("#keypad_delete").off("click", keypad_delete_pressed);
        $("#keypad_submit").off("click", keypad_submit_pressed);
        $("#keypad_decimal_point").off("click", keypad_decimal_point_pressed);
    }

    this.refreshDisplay = function () {
        if (valueStr == "")
            $("#display").html("---");
        else
            $("#display").html(valueStr.toString());
    }

    function keypad_number_pressed(event) {
        var digit = $(event.currentTarget).attr("digit");

        var resultStr = valueStr + digit;

        if (digit == "0") {
            if (valueStr != "0")
                valueStr += digit;
        } else {
            if (valueStr != "0")
                valueStr += digit;
        }

        self.refreshDisplay();
    }

    function keypad_delete_pressed(event) {
        if (valueStr != "")
            valueStr = valueStr.substring(0, valueStr.length - 1);

        self.refreshDisplay();
    }

    function keypad_decimal_point_pressed(event) {
        if (valueStr != "")
            valueStr += ",";

        self.refreshDisplay();
    }

    function keypad_submit_pressed(event) {
        var value = parseFloat(valueStr.replace(",", "."));

        var SERVER_URL = Preferences.SERVER_SCORE_URL;
        var SERVER_TIMEOUT = Preferences.SERVER_SCORE_TIMEOUT;

        $.ajax({
            url: SERVER_URL + "?value=" + value.toString(),
            timeout: SERVER_TIMEOUT,
            async: true,
            type: "POST",
            processData: false,
            success: function (result) {
                
            },
            error: function (request, status, error) {
                console.info("score error");
            }
        });
    }
}