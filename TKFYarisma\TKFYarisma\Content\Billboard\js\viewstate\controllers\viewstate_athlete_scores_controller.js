function ViewStateAthleteScoresController() {
    var self = this;
    var totalTime = 0;

    var timeoutId = undefined;
    var intervalId = undefined;

    var listItemCount = 0;
    var scrollerDirection = 1;
    var scrollerYOffset = 0;

    this.viewStateName = "athlete_scores";

    this.formatScore = function (score, tournamentStyle) {
        var scoreStr = score.toString();
        var dotIndex = scoreStr.indexOf(".");
        if (dotIndex != -1) {
            var scoreHighStr = scoreStr.substring(0, dotIndex);
            if (tournamentStyle == 1) {
                for (var i = 0; i < 2 - scoreHighStr.length; i++)
                    scoreHighStr = "0" + scoreHighStr;
            }
                
            var scoreLowStr = scoreStr.substring(dotIndex + 1, scoreStr.length);
            if (scoreLowStr.length < 2) {
                for (var i = 0; i < 2 - scoreLowStr.length; i++)
                    scoreLowStr = scoreLowStr + "0";
            }
            if (scoreLowStr.length > 2)
                scoreLowStr = scoreLowStr.substring(0, 2);
        } else {
            var scoreHighStr = scoreStr;
            if (tournamentStyle == 1) {
                for (var i = 0; i < 2 - scoreHighStr.length; i++)
                    scoreHighStr = "0" + scoreHighStr;
            }
                
            var scoreLowStr = "00";
        }

        return scoreHighStr + "." + scoreLowStr;
    }

    this.initialize = function (properties) {
        var $viewState = ElementHelper.getElementById($("body"), self.viewStateName);

        var tournamentStyle = ViewStateHelper.getProperty(properties, "tournament_style");
        var phaseTourCount = ViewStateHelper.getProperty(properties, "phase_tour_count");

        var tour = ViewStateHelper.getProperty(properties, "tour");

        var $list = $(ElementHelper.getElementById($viewState, "list"));
        var $row = $(ElementHelper.getElementById($list, "row"));
        var $scroller = $(ElementHelper.getElementById($list, "scroller"));
        
        $scroller.html("");

        var endPhase = ViewStateHelper.getProperty(properties, "end_phase");

        var $score1Title = $(ElementHelper.getElementById($viewState, "score_1_title"));
        var $score2Title = $(ElementHelper.getElementById($viewState, "score_2_title"));
        if (endPhase && tournamentStyle == 1) {
            $score1Title.text("TOPLAM PUAN");
            $score1Title.css("width", "280px");
            $score1Title.css("left", "1400px")
        } else {
            $score1Title.text("1. TUR");
            $score1Title.css("width", "225px");
            $score1Title.css("left", "1270px")
        }

        var scores = ViewStateHelper.getProperty(properties, "scores");
        if (scores != undefined) {
            listItemCount = scores.length;

            var rowIndex = 0;
            var rowYOffset = 30;
            for (var i = 0; i < scores.length; i++) {
                var score = scores[i];

                var $newRow = $row.clone();
                $scroller.append($newRow);
                
                $newRow.css("display", "block");

                var $rowAthleteOrderText = $(ElementHelper.getElementById($newRow, "athlete_order_text"));
                $rowAthleteOrderText.html(score.athlete_order);

                var $rowAthleteName = $(ElementHelper.getElementById($newRow, "athlete_name"));
                $rowAthleteName.html(score.athlete_name);

                var $rowAthleteCityName = $(ElementHelper.getElementById($newRow, "athlete_city_name"));
                $rowAthleteCityName.html(score.athlete_city_name);

                var score1 = score.score_1;
                var score2 = score.score_2;
                var score3 = score.score_3;
                var totalScore = score.total_score;
                
                var $rowScore1 = $(ElementHelper.getElementById($newRow, "score_1"));
                var $rowScore1Background = $(ElementHelper.getElementById($rowScore1, "background"));
                var $rowScore1Value = $(ElementHelper.getElementById($rowScore1, "value"));

                if (!endPhase || tournamentStyle == 2) {
                    if (tour > 1 && this.getHighestScoreOrder(score1, score2, score3) == 1) {
                        $rowScore1Background.attr("src", "../Content/Billboard/img/viewstate/viewstate_athlete_scores/score_background_highlighted.png");
                        $rowScore1Value.css("color", "#fff");
                    } else {
                        $rowScore1Background.attr("src", "../Content/Billboard/img/viewstate/viewstate_athlete_scores/score_background.png");
                        $rowScore1Value.css("color", "#000");
                    }

                    $rowScore1Value.text(this.formatScore(score1, tournamentStyle));
                } else {
                    $rowScore1Value.text(this.formatScore(totalScore, tournamentStyle));
                }

                if (!endPhase || tournamentStyle == 2) {
                    var $rowScore2 = $(ElementHelper.getElementById($newRow, "score_2"));
                    var $rowScore2Background = $(ElementHelper.getElementById($rowScore2, "background"));
                    var $rowScore2Value = $(ElementHelper.getElementById($rowScore2, "value"));

                    if (tour > 1 && this.getHighestScoreOrder(score1, score2, score3) == 2) {
                        $rowScore2Background.attr("src", "../Content/Billboard/img/viewstate/viewstate_athlete_scores/score_background_highlighted.png");
                        $rowScore2Value.css("color", "#fff");
                    } else {
                        $rowScore2Background.attr("src", "../Content/Billboard/img/viewstate/viewstate_athlete_scores/score_background.png");
                        $rowScore2Value.css("color", "#000");
                    }

                    $rowScore2Value.text(this.formatScore(score2, tournamentStyle));

                    var $rowScore3 = $(ElementHelper.getElementById($newRow, "score_3"));
                    var $rowScore3Background = $(ElementHelper.getElementById($rowScore3, "background"));
                    var $rowScore3Value = $(ElementHelper.getElementById($rowScore3, "value"));

                    if (tour > 1 && this.getHighestScoreOrder(score1, score2, score3) == 3) {
                        $rowScore3Background.attr("src", "../Content/Billboard/img/viewstate/viewstate_athlete_scores/score_background_highlighted.png");
                        $rowScore3Value.css("color", "#fff");
                    } else {
                        $rowScore3Background.attr("src", "../Content/Billboard/img/viewstate/viewstate_athlete_scores/score_background.png");
                        $rowScore3Value.css("color", "#000");
                    }

                    $rowScore2Value.text(this.formatScore(score2, tournamentStyle));
                    $rowScore3Value.text(this.formatScore(score3, tournamentStyle));
                }

                if (tour == 1 || (endPhase && tournamentStyle == 1)) {
                    // hide tour 2 column
                    var $score2Title = $(ElementHelper.getElementById($viewState, "score_2_title"));
                    $score2Title.css("display", "none");
                    var $rowScore2 = $(ElementHelper.getElementById($newRow, "score_2"));
                    $rowScore2.css("display", "none");

                    var $score3Title = $(ElementHelper.getElementById($viewState, "score_3_title"));
                    $score3Title.css("display", "none");
                    var $rowScore3 = $(ElementHelper.getElementById($newRow, "score_3"));
                    $rowScore3.css("display", "none");

                    // move tour 1 column to tour 2 location
                    var $score1Title = $(ElementHelper.getElementById($viewState, "score_1_title"));
                    $score1Title.css("left", $score2Title.css("left"));
                    $rowScore1.css("left", $rowScore2.css("left"));
                } else if (tour == 2) {
                    // hide tour 2 column
                    var $score2Title = $(ElementHelper.getElementById($viewState, "score_2_title"));
                    $score2Title.css("display", "block");
                    var $rowScore2 = $(ElementHelper.getElementById($newRow, "score_2"));
                    $rowScore2.css("display", "block");

                    var $score3Title = $(ElementHelper.getElementById($viewState, "score_3_title"));
                    $score3Title.css("display", "none");
                    var $rowScore3 = $(ElementHelper.getElementById($newRow, "score_3"));
                    $rowScore3.css("display", "none");

                    // move tour 1 column to tour 2 location
                    var $score1Title = $(ElementHelper.getElementById($viewState, "score_1_title"));
                    $score1Title.css("left", $score2Title.css("left"));
                    $rowScore1.css("left", $rowScore2.css("left"));
                } else if (tour == 3) {
                    var $score3Title = $(ElementHelper.getElementById($viewState, "score_3_title"));
                    $score3Title.css("display", "block");
                    var $rowScore3 = $(ElementHelper.getElementById($newRow, "score_3"));
                    $rowScore3.css("display", "block");

                    var $score2Title = $(ElementHelper.getElementById($viewState, "score_2_title"));
                    $score2Title.css("display", "block");
                    var $rowScore2 = $(ElementHelper.getElementById($newRow, "score_2"));
                    $rowScore2.css("display", "block");
                }
                
                var $rowQueue = $(ElementHelper.getElementById($newRow, "queue"));
                $rowQueue.css("display", score.is_queued ? "block" : "none");

                rowIndex++;
            }
        }

        scrollerDirection = 1;
        scrollerYOffset = 0;
        var $scroller = $(ElementHelper.getElementById($viewState, "scroller"));
        $scroller.css("top", scrollerYOffset);

        timeoutId = setTimeout(self.waitList, 3000);
    }

    this.waitList = function () {
        clearTimeout(timeoutId);
        intervalId = setInterval(self.scrollList, 10);
    }

    this.scrollList = function () {
        scrollerYOffset += scrollerDirection == 1 ? -2 : 2;

        var $viewstate = ElementHelper.getElementById($("body"), self.viewStateName);
        var $scroller = $(ElementHelper.getElementById($viewstate, "scroller"));
        $scroller.css("top", scrollerYOffset);

        if ((scrollerDirection == 1 && (scrollerYOffset < (-listItemCount * 130) + (6 * 130))) ||
            (scrollerDirection == 0 && scrollerYOffset > 0)) {
            clearInterval(intervalId);
            timeoutId = setTimeout(self.switchScrollerDirection, 3000);
        }
    }

    this.switchScrollerDirection = function () {
        clearTimeout(timeoutId);
        scrollerDirection = 1 - scrollerDirection;
        intervalId = setInterval(self.scrollList, 10);
    }

    this.deinitialize = function () {
        if (timeoutId != undefined)
            clearTimeout(timeoutId);

        if (intervalId != undefined)
            clearInterval(intervalId);
    }

    this.getHighestScoreOrder = function (score1, score2, score3) {
        if (score1 >= score2 && score1 >= score3)
            return 1;
        else if (score2 >= score1 && score2 >= score3)
            return 2;
        else if (score3 >= score1 && score3 >= score2)
            return 3;
        else
            return 0;
    }
}