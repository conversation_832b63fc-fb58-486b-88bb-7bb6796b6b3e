function ViewStateVideoPlayerController() {
    var self = this;
    var videoName = undefined;
    var $video = undefined;
    var timeoutId = undefined;

    this.viewStateName = "video_player";

    this.initialize = function (properties) {
        var $viewstate = ElementHelper.getElementById($("body"), self.viewStateName);

        clearTimeout(self.timeoutId);

        var url = ViewStateHelper.getProperty(properties, "url");

        $video = $(ElementHelper.getElementById($viewstate, "video"));
        $video.attr("src", url);
        $video[0].play();
    }

    this.deinitialize = function () {
        $video[0].pause();
    }
}