﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace TKFYarisma.Helpers
{
    public static class TableExtensions
    {
        public static IHtmlString IconLink(this HtmlHelper helper, string actionName, object routeValues, IconType iconType, IconSize iconSize, IconColor iconColor, string tooltip = "")
        {
            var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);

            var iconClassName = string.Empty;

            switch (iconType)
            {
                case IconType.Edit: iconClassName = "fa-edit"; break;
                case IconType.Delete: iconClassName = "fa-trash"; break;
                case IconType.Reports: iconClassName = "fa-clipboard-list"; break;
                case IconType.Settings: iconClassName = "fa-cog"; break;
                case IconType.Organizations: iconClassName = "fa-user-tie"; break;
                case IconType.LicensePacks: iconClassName = "fa-cubes"; break;
                case IconType.Licenses: iconClassName = "fa-unlock-alt"; break;
                case IconType.Groups: iconClassName = "fa-user-friends"; break;
                case IconType.Referees: iconClassName = "fa-user-tie"; break;
                case IconType.Athletes: iconClassName = "fa-user"; break;
                case IconType.Teams: iconClassName = "fa-users"; break;
                case IconType.Reglament: iconClassName = "fa-clipboard-check"; break;
                default: break;
            }

            var iconSizeClassName = string.Empty;

            switch (iconSize)
            {
                case IconSize.Default: break;
                case IconSize.Large: iconSizeClassName = "fa-lg"; break;
                case IconSize.Size2x: iconSizeClassName = "fa-2x"; break;
                case IconSize.Size3x: iconSizeClassName = "fa-3x"; break;
                case IconSize.Size4x: iconSizeClassName = "fa-4x"; break;
                case IconSize.Size5x: iconSizeClassName = "fa-5x"; break;
                default: break;
            }

            string iconColorClassName = string.Empty;

            switch (iconColor)
            {
                case IconColor.White: iconColorClassName = "icon-white"; break;
                case IconColor.Black: iconColorClassName = "icon-black"; break;
                default: break;
            }

            return helper.Raw("<a href='" + urlHelper.Action(actionName, routeValues) + "' title='" + tooltip + "'><div class='" + iconColorClassName + "'><span class='fa " + iconClassName + " " + iconSizeClassName + "'></span></div></a>");
        }

        public static IHtmlString IconLinkWithText(this HtmlHelper helper, string actionName, object routeValues, IconType iconType, IconSize iconSize, IconColor iconColor, string text, string tooltip = "")
        {
            var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);

            var iconClassName = string.Empty;

            switch (iconType)
            {
                case IconType.Edit: iconClassName = "fa-edit"; break;
                case IconType.Delete: iconClassName = "fa-trash"; break;
                case IconType.Reports: iconClassName = "fa-clipboard-list"; break;
                case IconType.Settings: iconClassName = "fa-cog"; break;
                case IconType.Organizations: iconClassName = "fa-user-tie"; break;
                case IconType.LicensePacks: iconClassName = "fa-cubes"; break;
                case IconType.Licenses: iconClassName = "fa-unlock-alt"; break;
                case IconType.Groups: iconClassName = "fa-user-friends"; break;
                case IconType.Referees: iconClassName = "fa-user-tie"; break;
                case IconType.Athletes: iconClassName = "fa-user"; break;
                case IconType.Teams: iconClassName = "fa-users"; break;
                case IconType.Reglament: iconClassName = "fa-clipboard-check"; break;
                default: break;
            }

            var iconSizeClassName = string.Empty;

            switch (iconSize)
            {
                case IconSize.Default: break;
                case IconSize.Large: iconSizeClassName = "fa-lg"; break;
                case IconSize.Size2x: iconSizeClassName = "fa-2x"; break;
                case IconSize.Size3x: iconSizeClassName = "fa-3x"; break;
                case IconSize.Size4x: iconSizeClassName = "fa-4x"; break;
                case IconSize.Size5x: iconSizeClassName = "fa-5x"; break;
                default: break;
            }

            string iconColorClassName = string.Empty;

            switch (iconColor)
            {
                case IconColor.White: iconColorClassName = "icon-white"; break;
                case IconColor.Black: iconColorClassName = "icon-black"; break;
                default: break;
            }

            return helper.Raw("<a href='" + urlHelper.Action(actionName, routeValues) + "' title='" + tooltip + "'><div class='icon-and-text-container'><div class='" + iconColorClassName + "'><span class='fa " + iconClassName + " " + iconSizeClassName + " icon-gap'></span></div>" + text + "</div></a>");
        }
    }
}