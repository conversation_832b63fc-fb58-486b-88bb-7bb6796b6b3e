﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace TKFYarisma.Helpers
{
    public class BreadCrumb
    {
        private string formatText;

        public BreadCrumb(string formatText, params object[] parameters)
        {
            this.formatText = formatText;
        }

        public static string ToHtml(string breadCrumb)
        {
            var sections = breadCrumb.Split('\\');
            if (sections.Length > 0)
            {
                var result = string.Empty;

                foreach (var section in sections)
                {
                    var startIndex = -1;
                    while ((startIndex = section.IndexOf("[link")) != -1)
                    {
                        if (startIndex != -1)
                        {
                            var endIndex = section.IndexOf("[/link]");
                            if (endIndex != -1)
                            {
                                var link = section.Substring(startIndex, endIndex - startIndex);
                                var linkTokens = link.Split(':');
                                if (linkTokens.Length > 1)
                                {
                                    var linkContent = linkTokens[1];

                                    // controller name
                                    var controllerName = string.Empty;
                                    var controllerIndex = linkContent.IndexOf("controller");
                                    if (controllerIndex != -1)
                                    {
                                        var si = linkContent.IndexOf("'", controllerIndex);
                                        if (si != -1)
                                        {
                                            var ei = linkContent.IndexOf("'", si + 1);
                                            if (ei != -1)
                                                controllerName = linkContent.Substring(si, ei - si);
                                        }
                                    }

                                    // action name
                                    var actionName = string.Empty;
                                    var actionIndex = linkContent.IndexOf("action");
                                    if (actionIndex != -1)
                                    {
                                        var si = linkContent.IndexOf("'", actionIndex);
                                        if (si != -1)
                                        {
                                            var ei = linkContent.IndexOf("'", si + 1);
                                            if (ei != -1)
                                                actionName = linkContent.Substring(si, ei - si);
                                        }
                                    }

                                    //if (!string.IsNullOrEmpty(controllerName))

                                }
                            }
                        }
                    }

                    result += section;
                }

                return result;
            }
            else
                return string.Empty;
        }
    }
}