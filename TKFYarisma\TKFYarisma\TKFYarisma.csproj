﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="..\packages\Microsoft.Net.Compilers.2.6.1\build\Microsoft.Net.Compilers.props" Condition="Exists('..\packages\Microsoft.Net.Compilers.2.6.1\build\Microsoft.Net.Compilers.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{DA44FCE7-DF49-4903-8B2B-88B5636695A9}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>TKFYarisma</RootNamespace>
    <AssemblyName>TKFYarisma</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>false</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus, Version=4.5.2.1, Culture=neutral, PublicKeyToken=ea159fdaa78159a1, processorArchitecture=MSIL">
      <HintPath>..\packages\EPPlus.4.5.2.1\lib\net40\EPPlus.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="PresentationCore" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.4\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.2.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.4\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Optimization">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.4\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.4\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.4\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.4\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>..\packages\Newtonsoft.Json.11.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease">
      <Private>True</Private>
      <HintPath>..\packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime">
      <Private>True</Private>
      <HintPath>..\packages\Antlr.3.5.0.2\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System.Diagnostics.DiagnosticSource">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.4.4.1\lib\net46\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.TelemetryCorrelation">
      <HintPath>..\packages\Microsoft.AspNet.TelemetryCorrelation.1.0.0\lib\net45\Microsoft.AspNet.TelemetryCorrelation.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights">
      <HintPath>..\packages\Microsoft.ApplicationInsights.2.5.1\lib\net46\Microsoft.ApplicationInsights.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.Agent.Intercept">
      <HintPath>..\packages\Microsoft.ApplicationInsights.Agent.Intercept.2.4.0\lib\net45\Microsoft.AI.Agent.Intercept.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.DependencyCollector">
      <HintPath>..\packages\Microsoft.ApplicationInsights.DependencyCollector.2.5.1\lib\net45\Microsoft.AI.DependencyCollector.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.PerfCounterCollector">
      <HintPath>..\packages\Microsoft.ApplicationInsights.PerfCounterCollector.2.5.1\lib\net45\Microsoft.AI.PerfCounterCollector.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.ServerTelemetryChannel">
      <HintPath>..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.5.1\lib\net45\Microsoft.AI.ServerTelemetryChannel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.WindowsServer">
      <HintPath>..\packages\Microsoft.ApplicationInsights.WindowsServer.2.5.1\lib\net45\Microsoft.AI.WindowsServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.Web">
      <HintPath>..\packages\Microsoft.ApplicationInsights.Web.2.5.1\lib\net45\Microsoft.AI.Web.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Content Include="Content\Billboard\css\viewstate\viewstate_athlete_orders.css" />
    <Content Include="Content\Billboard\css\viewstate\viewstate_referees.css" />
    <Content Include="Content\Billboard\css\viewstate\viewstate_athlete_info.css" />
    <Content Include="Content\Billboard\css\viewstate\viewstate_athlete_score.css" />
    <Content Include="Content\Billboard\css\viewstate\viewstate_athlete_scores.css" />
    <Content Include="Content\Billboard\css\viewstate\viewstate_athlete_timer.css" />
    <Content Include="Content\Billboard\css\viewstate\viewstate_default.css" />
    <Content Include="Content\Billboard\css\viewstate\viewstate_medal_ceromony.css" />
    <Content Include="Content\Billboard\css\viewstate\viewstate_video_player.css" />
    <Content Include="Content\Billboard\img\viewstate\viewstate_athlete_orders\order_background.png" />
    <Content Include="Content\Billboard\img\viewstate\viewstate_athlete_orders\scores_row_background.png" />
    <Content Include="Content\Billboard\img\viewstate\viewstate_athlete_orders\score_background.png" />
    <Content Include="Content\Billboard\img\viewstate\viewstate_athlete_orders\score_background_highlighted.png" />
    <Content Include="Content\Billboard\img\helmet.png" />
    <Content Include="Content\Billboard\img\viewstate\viewstate_athlete_score\athlete_score_small_background.png" />
    <Content Include="Content\Billboard\img\viewstate\viewstate_referees\photo_canvas_referee.png" />
    <Content Include="Content\Billboard\img\viewstate\viewstate_referees\photo_canvas_referee_mask.png" />
    <Content Include="Content\Billboard\js\preferences.js" />
    <Content Include="Content\Billboard\js\viewstate\controllers\viewstate_athlete_info_controller.js" />
    <Content Include="Content\Billboard\js\viewstate\controllers\viewstate_athlete_orders_controller.js" />
    <Content Include="Content\Billboard\js\viewstate\controllers\viewstate_referees_controller.js" />
    <Content Include="Content\Tablet\css\viewstate\viewstate_default.css" />
    <Content Include="Content\Tablet\css\viewstate\viewstate_timing.css" />
    <Content Include="Content\Tablet\js\preferences.js" />
    <Content Include="Content\Tablet\js\viewstate\controllers\viewstate_timing_controller.js" />
    <Content Include="Content\Tablet\js\viewstate\controllers\viewstate_scoring_controller.js" />
    <Content Include="Content\Tablet\js\viewstate\controllers\viewstate_default_controller.js" />
    <Content Include="Content\Tablet\js\viewstate\core\element_helper.js" />
    <Content Include="Content\Tablet\js\viewstate\core\viewstate_helper.js" />
    <Content Include="Content\Tablet\js\viewstate\core\viewstate_main.js" />
    <Content Include="Content\Billboard\js\viewstate\viewstate.js" />
    <Content Include="Content\Tablet\css\viewstate\viewstate_scoring.css" />
    <Compile Include="Controllers\TabletController.cs" />
    <Compile Include="Enum\TournamentStyle.cs" />
    <Compile Include="Filters\AllowCrossSiteAttribute.cs" />
    <Compile Include="Controllers\BillboardController.cs" />
    <Compile Include="Controllers\ControlPanelController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Filters\SecurityAttribute.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Helpers\BreadCrumb.cs" />
    <Compile Include="Helpers\HashHelper.cs" />
    <Compile Include="Helpers\HtmlExtensions.cs" />
    <Compile Include="Helpers\IconColor.cs" />
    <Compile Include="Helpers\IconSize.cs" />
    <Compile Include="Helpers\IconType.cs" />
    <Compile Include="Helpers\PathHelper.cs" />
    <Compile Include="Helpers\ScoreHelper.cs" />
    <Compile Include="Helpers\TableExtensions.cs" />
    <Compile Include="Helpers\TKFTabletHelper.cs" />
    <Compile Include="Helpers\TKFBillboardHelper.cs" />
    <Compile Include="Helpers\TournamentPresidentInfo.cs" />
    <Compile Include="Models\Athlete.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\AthleteModel.cs" />
    <Compile Include="Models\Billboard.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\ControlCenter.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\ControlCenterModels.cs" />
    <Compile Include="Models\District.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\DistrictCity.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\Gender.cs" />
    <Compile Include="Models\GenderHelper.cs" />
    <Compile Include="Models\Model.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Model.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\Model.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\Model.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Model.edmx</DependentUpon>
    </Compile>
    <Compile Include="Models\ProcedureGetAthleteScores_Result.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\Referee.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\RefereeModel.cs" />
    <Compile Include="Models\RefereeType.cs" />
    <Compile Include="Models\SessionUser.cs" />
    <Compile Include="Models\SignInErrorType.cs" />
    <Compile Include="Models\SignInModel.cs" />
    <Compile Include="Models\Tablet.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\TabletInterfaceViewModel.cs" />
    <Compile Include="Models\TabletRefereeInfo.cs" />
    <Compile Include="Models\Tournament.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\TournamentDay.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\TournamentGroup.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\TournamentGroupAthlete.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\TournamentGroupAthleteRefereeScore.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\TournamentGroupAthleteScore.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\TournamentGroupMedal.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\TournamentGroupReferee.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\TournamentGroupTeam.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\TournamentGroupTeamAthlete.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\TournamentModels.cs" />
    <Compile Include="Models\TournamentGroupRefereeInfo.cs" />
    <Compile Include="Models\TournamentPhas.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\TournamentReferee.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\TournamentVideo.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\User.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\ViewAthlete.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\ViewReferee.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\ViewTournament.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\ViewTournamentGroupAthlete.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\ViewTournamentGroupReferee.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\ViewTournamentReferee.cs">
      <DependentUpon>Model.tt</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\Billboard\css\animate.css" />
    <Content Include="Content\Billboard\css\styles.css" />
    <Content Include="Content\Billboard\img\adem_ustaoglu.jpg" />
    <Content Include="Content\Billboard\img\athlete_name_stripe_blue.png" />
    <Content Include="Content\Billboard\img\athlete_name_stripe_green.png" />
    <Content Include="Content\Billboard\img\background_athlete_info_blue.jpg" />
    <Content Include="Content\Billboard\img\background_athlete_info_green.jpg" />
    <Content Include="Content\Billboard\img\background_default.jpg" />
    <Content Include="Content\Billboard\img\photo_mask.png" />
    <Content Include="Content\Billboard\img\viewstate\viewstate_athlete_timer\timer_background.png" />
    <Content Include="Content\Billboard\img\tkf_intro.png" />
    <Content Include="Content\Billboard\img\tournament_name_stripe_blue.png" />
    <Content Include="Content\Billboard\img\tourtnament_name_stripe_green.png" />
    <Content Include="Content\Billboard\img\viewstate\viewstate_athlete_scores\order_background.png" />
    <Content Include="Content\Billboard\img\viewstate\viewstate_athlete_scores\scores_row_background.png" />
    <Content Include="Content\Billboard\img\viewstate\viewstate_athlete_scores\score_background.png" />
    <Content Include="Content\Billboard\img\viewstate\viewstate_athlete_scores\score_background_highlighted.png" />
    <Content Include="Content\Billboard\img\viewstate\viewstate_athlete_score\athlete_order_background.png" />
    <Content Include="Content\Billboard\img\viewstate\viewstate_athlete_score\athlete_score_background.png" />
    <Content Include="Content\Billboard\img\viewstate\viewstate_athlete_score\athlete_score_background_highlighted.png" />
    <Content Include="Content\Billboard\img\viewstate\viewstate_medal_ceromony\photo_canvas_bronze.png" />
    <Content Include="Content\Billboard\img\viewstate\viewstate_medal_ceromony\photo_canvas_gold.png" />
    <Content Include="Content\Billboard\img\viewstate\viewstate_medal_ceromony\photo_canvas_mask.png" />
    <Content Include="Content\Billboard\img\viewstate\viewstate_medal_ceromony\photo_canvas_silver.png" />
    <Content Include="Content\Billboard\js\viewstate\core\element_helper.js" />
    <Content Include="Content\Billboard\js\libraries\jquery-3.3.1.min.js" />
    <Content Include="Content\Billboard\js\viewstate\controllers\viewstate_athlete_score_controller.js" />
    <Content Include="Content\Billboard\js\viewstate\controllers\viewstate_athlete_scores_controller.js" />
    <Content Include="Content\Billboard\js\viewstate\controllers\viewstate_athlete_timer_controller.js" />
    <Content Include="Content\Billboard\js\viewstate\controllers\viewstate_default_controller.js" />
    <Content Include="Content\Billboard\js\viewstate\core\viewstate_helper.js" />
    <Content Include="Content\Billboard\js\viewstate\core\viewstate_main.js" />
    <Content Include="Content\Billboard\js\viewstate\controllers\viewstate_medal_ceromony_controller.js" />
    <Content Include="Content\Billboard\js\viewstate\controllers\viewstate_video_player_controller.js" />
    <Content Include="Content\Billboard\video\tkf_intro.mp4" />
    <Content Include="Content\Billboard\video\video2.mp4" />
    <Content Include="Content\Tablet\css\animate.css" />
    <Content Include="Content\Tablet\css\bootstrap-grid.css" />
    <Content Include="Content\Tablet\css\bootstrap-grid.min.css" />
    <Content Include="Content\Tablet\css\bootstrap-reboot.css" />
    <Content Include="Content\Tablet\css\bootstrap-reboot.min.css" />
    <Content Include="Content\Tablet\css\bootstrap.css" />
    <Content Include="Content\Tablet\css\bootstrap.min.css" />
    <Content Include="Content\Tablet\css\styles.css" />
    <Content Include="Content\Tablet\img\arrow.png" />
    <Content Include="Content\Tablet\img\highlight.png" />
    <Content Include="Content\Tablet\img\logo.png" />
    <Content Include="Content\Tablet\img\tablet_screen1.jpg" />
    <Content Include="Content\Tablet\img\tkf_timer_background.jpg" />
    <Content Include="Content\Tablet\img\tkf_timer_background_landspace.jpg" />
    <Content Include="Content\Tablet\js\libraries\bootstrap.bundle.js" />
    <Content Include="Content\Tablet\js\libraries\bootstrap.bundle.min.js" />
    <Content Include="Content\Tablet\js\libraries\bootstrap.js" />
    <Content Include="Content\Tablet\js\libraries\bootstrap.min.js" />
    <Content Include="Content\Tablet\js\libraries\jquery-3.3.1.min.js" />
    <Content Include="Content\Tablet\js\libraries\jquery-ui.min.js" />
    <Content Include="Content\Tablet\js\viewstate\viewstate.js" />
    <Content Include="Content\Site\images\logo.png" />
    <Content Include="Content\Site\scripts\bootstrap-datetimepicker.min.js" />
    <Content Include="Content\Site\scripts\bootstrap.bundle.js" />
    <Content Include="Content\Site\scripts\bootstrap.bundle.min.js" />
    <Content Include="Content\Site\scripts\bootstrap.js" />
    <Content Include="Content\Site\scripts\bootstrap.min.js" />
    <Content Include="Content\Billboard\font\Anton-Regular.ttf" />
    <Content Include="Content\Billboard\font\Exo-BlackItalic.ttf" />
    <Content Include="Content\Billboard\font\Exo-BoldItalic.ttf" />
    <Content Include="Content\Billboard\font\FranklinGothic-MediumItalic.ttf" />
    <Content Include="Content\Billboard\font\Oswald-Bold.ttf" />
    <Content Include="Content\Billboard\font\Oswald-ExtraLight.ttf" />
    <Content Include="Content\Billboard\font\Oswald-Light.ttf" />
    <Content Include="Content\Billboard\font\Oswald-Medium.ttf" />
    <Content Include="Content\Billboard\font\Oswald-Regular.ttf" />
    <Content Include="Content\Billboard\font\Oswald-SemiBold.ttf" />
    <Content Include="Content\Tablet\css\bootstrap-grid.css.map" />
    <Content Include="Content\Tablet\css\bootstrap-grid.min.css.map" />
    <Content Include="Content\Tablet\css\bootstrap-reboot.css.map" />
    <Content Include="Content\Tablet\css\bootstrap-reboot.min.css.map" />
    <Content Include="Content\Tablet\css\bootstrap.css.map" />
    <Content Include="Content\Tablet\css\bootstrap.min.css.map" />
    <Content Include="Content\Billboard\font\MyriadPro-BlackSemiCn.otf" />
    <Content Include="Content\Billboard\font\MyriadPro-Bold.otf" />
    <None Include="Content\Site\scripts\jquery-3.3.1.intellisense.js" />
    <Content Include="Content\Site\scripts\jquery-3.3.1.js" />
    <Content Include="Content\Site\scripts\jquery-3.3.1.min.js" />
    <Content Include="Content\Site\scripts\jquery-3.3.1.slim.js" />
    <Content Include="Content\Site\scripts\jquery-3.3.1.slim.min.js" />
    <None Include="Content\Site\scripts\jquery.validate-vsdoc.js" />
    <Content Include="Content\Site\scripts\jquery.validate.js" />
    <Content Include="Content\Site\scripts\jquery.validate.min.js" />
    <Content Include="Content\Site\scripts\jquery.validate.unobtrusive.js" />
    <Content Include="Content\Site\scripts\jquery.validate.unobtrusive.min.js" />
    <Content Include="Content\Site\scripts\modernizr-2.8.3.js" />
    <Content Include="Content\Site\scripts\moment-with-locales.min.js" />
    <Content Include="Content\Site\styles\bootstrap-datetimepicker.css" />
    <Content Include="Content\Site\styles\bootstrap-datetimepicker.min.css" />
    <Content Include="Content\Site\styles\bootstrap-grid.css" />
    <Content Include="Content\Site\styles\bootstrap-grid.min.css" />
    <Content Include="Content\Site\styles\bootstrap-helper.css" />
    <Content Include="Content\Site\styles\bootstrap-reboot.css" />
    <Content Include="Content\Site\styles\bootstrap-reboot.min.css" />
    <Content Include="Content\Site\styles\bootstrap.css" />
    <Content Include="Content\Site\styles\bootstrap.min.css" />
    <Content Include="Content\Site\styles\fa-brands.css" />
    <Content Include="Content\Site\styles\fa-brands.min.css" />
    <Content Include="Content\Site\styles\fa-regular.css" />
    <Content Include="Content\Site\styles\fa-regular.min.css" />
    <Content Include="Content\Site\styles\fa-solid.css" />
    <Content Include="Content\Site\styles\fa-solid.min.css" />
    <Content Include="Content\Site\styles\font-awesome.css" />
    <Content Include="Content\Site\styles\font-awesome.min.css" />
    <Content Include="Content\Site\styles\fontawesome-all.css" />
    <Content Include="Content\Site\styles\fontawesome-all.min.css" />
    <Content Include="Content\Site\styles\fontawesome.css" />
    <Content Include="Content\Site\styles\fontawesome.min.css" />
    <Content Include="Content\Site\styles\site.css" />
    <Content Include="Content\Site\styles\table.css" />
    <Content Include="Content\Site\styles\webgrid.css" />
    <Content Include="Content\Site\webfonts\fa-brands-400.svg" />
    <Content Include="Content\Site\webfonts\fa-regular-400.svg" />
    <Content Include="Content\Site\webfonts\fa-solid-900.svg" />
    <Content Include="Content\Site\webfonts\fontawesome-webfont.svg" />
    <Content Include="Content\Site\webfonts\glyphicons-halflings-regular.svg" />
    <Content Include="favicon.ico" />
    <Content Include="Global.asax" />
    <Content Include="Models\Model.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>Model.Context.cs</LastGenOutput>
      <DependentUpon>Model.edmx</DependentUpon>
    </Content>
    <Content Include="Models\Model.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>Model.edmx</DependentUpon>
      <LastGenOutput>Model.cs</LastGenOutput>
    </Content>
    <Content Include="ApplicationInsights.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <EntityDeploy Include="Models\Model.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>Model.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <Content Include="Models\Model.edmx.diagram">
      <DependentUpon>Model.edmx</DependentUpon>
    </Content>
    <Content Include="Uploads\Video\video2.mp4" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Views\Web.config" />
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Shared\Error.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="Views\ControlPanel\Index.cshtml" />
    <Content Include="Views\ControlPanel\SignIn.cshtml" />
    <Content Include="Views\ControlPanel\ControlCenter.cshtml" />
    <Content Include="Views\ControlPanel\Preferences.cshtml" />
    <Content Include="Views\ControlPanel\Tournaments.cshtml" />
    <Content Include="Views\ControlPanel\CreateTournament.cshtml" />
    <Content Include="Views\ControlPanel\EditTournament.cshtml" />
    <Content Include="Views\ControlPanel\DeleteTournament.cshtml" />
    <Content Include="Views\ControlPanel\Athletes.cshtml" />
    <Content Include="Views\ControlPanel\CreateAthlete.cshtml" />
    <Content Include="Views\ControlPanel\EditAthlete.cshtml" />
    <Content Include="Views\ControlPanel\DeleteAthlete.cshtml" />
    <Content Include="Views\ControlPanel\Referees.cshtml" />
    <Content Include="Views\ControlPanel\CreateReferee.cshtml" />
    <Content Include="Views\ControlPanel\EditReferee.cshtml" />
    <Content Include="Views\ControlPanel\DeleteReferee.cshtml" />
    <Content Include="Views\Billboard\Interface.cshtml" />
    <Content Include="Views\ControlPanel\ImportAthletes.cshtml" />
    <Content Include="Views\ControlPanel\TournamentGroups.cshtml" />
    <Content Include="Views\ControlPanel\CreateTournamentGroup.cshtml" />
    <Content Include="Views\ControlPanel\EditTournamentGroup.cshtml" />
    <Content Include="Views\ControlPanel\DeleteTournamentGroup.cshtml" />
    <Content Include="Views\ControlPanel\TournamentGroupReferees.cshtml" />
    <Content Include="Views\ControlPanel\TournamentReglament.cshtml" />
    <Content Include="Views\ControlPanel\TournamentGroupAthletes.cshtml" />
    <Content Include="Views\Tablet\Interface.cshtml" />
    <Content Include="Views\ControlPanel\AddTournamentGroupAthletes.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Uploads\Athlete\Import\" />
    <Folder Include="Uploads\Athlete\Photo\" />
    <Folder Include="Uploads\Referee\Photo\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\Site\scripts\bootstrap.bundle.js.map" />
    <Content Include="Content\Site\scripts\bootstrap.bundle.min.js.map" />
    <Content Include="Content\Site\scripts\bootstrap.js.map" />
    <Content Include="Content\Site\scripts\bootstrap.min.js.map" />
    <Content Include="Content\Site\scripts\jquery-3.3.1.min.map" />
    <Content Include="Content\Site\scripts\jquery-3.3.1.slim.min.map" />
    <Content Include="Content\Site\styles\bootstrap-grid.css.map" />
    <Content Include="Content\Site\styles\bootstrap-grid.min.css.map" />
    <Content Include="Content\Site\styles\bootstrap-reboot.css.map" />
    <Content Include="Content\Site\styles\bootstrap-reboot.min.css.map" />
    <Content Include="Content\Site\styles\bootstrap.css.map" />
    <Content Include="Content\Site\styles\bootstrap.min.css.map" />
    <Content Include="Content\Site\styles\font-awesome.css.map" />
    <Content Include="Content\Site\webfonts\fa-brands-400.eot" />
    <Content Include="Content\Site\webfonts\fa-brands-400.ttf" />
    <Content Include="Content\Site\webfonts\fa-brands-400.woff" />
    <Content Include="Content\Site\webfonts\fa-brands-400.woff2" />
    <Content Include="Content\Site\webfonts\fa-regular-400.eot" />
    <Content Include="Content\Site\webfonts\fa-regular-400.ttf" />
    <Content Include="Content\Site\webfonts\fa-regular-400.woff" />
    <Content Include="Content\Site\webfonts\fa-regular-400.woff2" />
    <Content Include="Content\Site\webfonts\fa-solid-900.eot" />
    <Content Include="Content\Site\webfonts\fa-solid-900.ttf" />
    <Content Include="Content\Site\webfonts\fa-solid-900.woff" />
    <Content Include="Content\Site\webfonts\fa-solid-900.woff2" />
    <Content Include="Content\Site\webfonts\fontawesome-webfont.eot" />
    <Content Include="Content\Site\webfonts\fontawesome-webfont.ttf" />
    <Content Include="Content\Site\webfonts\fontawesome-webfont.woff" />
    <Content Include="Content\Site\webfonts\fontawesome-webfont.woff2" />
    <Content Include="Content\Site\webfonts\FontAwesome.otf" />
    <Content Include="Content\Site\webfonts\glyphicons-halflings-regular.eot" />
    <Content Include="Content\Site\webfonts\glyphicons-halflings-regular.ttf" />
    <Content Include="Content\Site\webfonts\glyphicons-halflings-regular.woff" />
    <Content Include="Content\Site\webfonts\glyphicons-halflings-regular.woff2" />
    <Content Include="Content\Tablet\fonts\DINTBL.TTF" />
    <Content Include="Content\Tablet\fonts\DINTBOLD.TTF" />
    <Content Include="Content\Tablet\fonts\DINTL_.TTF" />
    <Content Include="Content\Tablet\fonts\DINTM_.TTF" />
    <Content Include="Content\Tablet\fonts\DINT__.TTF" />
    <Content Include="Content\Tablet\fonts\RobotoCondensed-Bold.ttf" />
    <Content Include="Content\Tablet\fonts\RobotoCondensed-BoldItalic.ttf" />
    <Content Include="Content\Tablet\fonts\RobotoCondensed-Italic.ttf" />
    <Content Include="Content\Tablet\fonts\RobotoCondensed-Light.ttf" />
    <Content Include="Content\Tablet\fonts\RobotoCondensed-LightItalic.ttf" />
    <Content Include="Content\Tablet\fonts\RobotoCondensed-Regular.ttf" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>55389</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost/TKFYarisma</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.Net.Compilers.2.6.1\build\Microsoft.Net.Compilers.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Net.Compilers.2.6.1\build\Microsoft.Net.Compilers.props'))" />
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>