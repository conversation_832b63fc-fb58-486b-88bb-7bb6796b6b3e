#athlete_info #background {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
}

#athlete_info #tournament {
    position: absolute;
    left: 500px;
    top: 140px;
    width: 1420px;
    height: 60px;
}

    #athlete_info #tournament #background {
        position: absolute;
        right: 0px;
        top: 0px;
        width: auto;
        height: auto;
    }

    #athlete_info #tournament #tournament_name_and_phase {
        color: #f0f0f0;
        position: absolute;
        padding-right: 0px;
        right: 70px;
        top: 10px;
        width: 1250px;
        height: 350px;
        font-size: 30pt;
        text-align: right;
        line-height: 30pt;
        letter-spacing: 2px;
        text-shadow: 0px 0px 5px #800000;
    }

#athlete_info #athlete {
    position: absolute;
    left: 300px;
    top: 220px;
    width: 1620px;
    height: 600px;
}

    #athlete_info #athlete #background {
        position: absolute;
        left: 0px;
        top: 140px;
        width: auto;
        height: auto;
    }

#athlete_info #photo {
    position: absolute;
    left: 200px;
    top: 185px;
    width: 675px;
    height: 675px;
    object-fit: contain;
    -webkit-mask-image: url(../../img/photo_mask.png);
    mask-image: url(../img/photo_mask.png);
    -webkit-mask-position: center center;
    mask-position: center center;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
}

#athlete_info #helmet {
    position: absolute;
    left: 650px;
    top: 685px;
    width: 150px;
    height: 150px;
    object-fit: contain;
}

#athlete_info #athlete #name {
    color: #f0f0f0;
    position: absolute;
    left: 600px;
    top: 190px;
    width: 1050px;
    height: auto;
    font-size: 80pt;
    line-height: 80pt;
    text-shadow: 2px 2px 5px #800000;
}

#athlete_info #athlete #district_and_city {
    color: #f0f0f0;
    position: absolute;
    left: 600px;
    top: 310px;
    width: 1050px;
    height: auto;
    font-size: 40pt;
    line-height: 40pt;
    text-shadow: 2px 2px 5px #800000;
}
