var ElementHelper = {};

ElementHelper.getElementById = function (parent, elementId) {
    var elements = $(parent).find("#" + elementId);
    if (elements != undefined && elements.length > 0)
        return elements[0];
    else
        return undefined;
}

ElementHelper.getElementByClassName = function (parent, className) {
    var elements = $(parent).find("." + className);
    if (elements != undefined && elements.length > 0)
        return elements[0];
    else
        return undefined;
}