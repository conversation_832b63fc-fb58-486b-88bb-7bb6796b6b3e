﻿@using TKFYarisma.Helpers

@model IEnumerable<TKFYarisma.Models.Tournament>

@{
    ViewBag.Title = "Yarışmalar";
    ViewBag.Section = "Tournaments";
}

<div class="row">
    <div class="col">
        <div class="tile-header tile-header-big">
            <div class="row">
                <div class="col mr-auto">
                    <h3>Yarışmalar</h3>
                </div>
                <div class="col-auto text-right">
                    <a href="@Url.Action("CreateTournament")" class="btn btn-dark"><i class="icon fas fa-plus"></i>Yarışma Oluştur</a>
                </div>
            </div>
        </div>
        <div class="tile">
            <div class="row">
                <div class="col">
                    @if (Model.Count() > 0)
                    {
                        var grid = new WebGrid(Model,
                                                canPage: true,
                                                rowsPerPage: 5,
                                                selectionFieldName: "selectedRow",
                                                ajaxUpdateContainerId: "gridContent");

                        var gridColumns = new List<WebGridColumn>();

                        gridColumns.Add(grid.Column("Name", "Yarışma Adı", style: "table-icon-column w-100"));
                        gridColumns.Add(grid.Column("Id", " ", format: (item) => { return Html.IconLink("TournamentReglament", new { id = item.Id }, IconType.Reglament, IconSize.Large, IconColor.Black, "Reglaman"); }, style: "table-icon-column"));
                        gridColumns.Add(grid.Column("Id", " ", format: (item) => { return Html.IconLink("TournamentGroups", new { id = item.Id }, IconType.Groups, IconSize.Large, IconColor.Black, "Gruplar"); }, style: "table-icon-column"));
                        gridColumns.Add(grid.Column("Id", " ", format: (item) => { return Html.IconLink("EditTournament", new { id = item.Id }, IconType.Edit, IconSize.Large, IconColor.Black, "Düzenle"); }, style: "table-icon-column"));
                        gridColumns.Add(grid.Column("Id", " ", format: (item) => { return Html.IconLink("DeleteTournament", new { id = item.Id }, IconType.Delete, IconSize.Large, IconColor.Black, "Sil"); }, style: "table-icon-column"));

                        grid.Pager(WebGridPagerModes.All);

                        <div id="gridContent">
                            @grid.GetHtml(
                                        tableStyle: "table table-hover table-bordered",
                                        headerStyle: "sorting_asc",
                                        footerStyle: "table-footer",
                                        rowStyle: "even",
                                        alternatingRowStyle: "odd",
                                        selectedRowStyle: "webgrid-selected-row",
                                        columns: gridColumns)
                        </div>
                    }
                    else
                    {
                        <div class="no-record">
                            There is no product record yet.
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
