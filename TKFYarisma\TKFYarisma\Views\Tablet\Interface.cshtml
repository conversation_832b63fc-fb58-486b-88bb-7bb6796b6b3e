﻿@model TKFYarisma.Models.TabletInterfaceViewModel

@{
    Layout = null;
}

<!DOCTYPE html>
<html lang="tr">

<head>
    <title>TKF Tablet Arayüzü</title>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />

    <script src="@Url.Content("~/Content/Tablet/js/libraries/jquery-3.3.1.min.js")"></script>
    <script src="@Url.Content("~/Content/Tablet/js/preferences.js")"></script>
    <script src="@Url.Content("~/Content/Tablet/js/viewstate/core/viewstate_helper.js")"></script>
    <script src="@Url.Content("~/Content/Tablet/js/viewstate/core/element_helper.js")"></script>
    <script src="@Url.Content("~/Content/Tablet/js/viewstate/viewstate.js")"></script>
    <script src="@Url.Content("~/Content/Tablet/js/viewstate/controllers/viewstate_default_controller.js")"></script>
    <script src="@Url.Content("~/Content/Tablet/js/viewstate/controllers/viewstate_scoring_controller.js")"></script>
    <script src="@Url.Content("~/Content/Tablet/js/viewstate/controllers/viewstate_timing_controller.js")"></script>
    <script src="@Url.Content("~/Content/Tablet/js/viewstate/core/viewstate_main.js")"></script>

    <link rel="stylesheet" href="@Url.Content("~/Content/Tablet/css/animate.css")">
    <link rel="stylesheet" href="@Url.Content("~/Content/Tablet/css/styles.css")" />
    <link rel="stylesheet" href="@Url.Content("~/Content/Tablet/css/viewstate/viewstate_default.css")">
    <link rel="stylesheet" href="@Url.Content("~/Content/Tablet/css/viewstate/viewstate_scoring.css")">
    <link rel="stylesheet" href="@Url.Content("~/Content/Tablet/css/viewstate/viewstate_timing.css")">
</head>

<body>
    <viewstate id="default">
        <img id="background" src="@Url.Content("~/Content/Tablet/img/tablet_screen1.jpg")" />
        <div id="container">
            <img id="logo" src="~/Content/Tablet/img/logo.png" /><br />
            <span id="title">TÜRKİYE KAYKAY FEDERASYONU</span>
        </div>
    </viewstate>

    <viewstate id="scoring">
        <img id="background" src="@Url.Content("~/Content/Tablet/img/tablet_screen1.jpg")" />
        <div id="container">
            <div class="keypad-column">
                <div id="keypad">
                    <div id="display-container">
                        <span id="display">---</span>
                    </div>
                    <div id="keypad_container">
                        <div id="keypad_row">
                            <button id="keypad_1" class="keypad-number-button" digit="1" type="button">1</button>
                            <button id="keypad_2" class="keypad-number-button" digit="2" type="button">2</button>
                            <button id="keypad_3" class="keypad-number-button" digit="3" type="button">3</button>
                        </div>
                        <div id="keypad_row">
                            <button id="keypad_4" class="keypad-number-button" digit="4" type="button">4</button>
                            <button id="keypad_5" class="keypad-number-button" digit="5" type="button">5</button>
                            <button id="keypad_6" class="keypad-number-button" digit="6" type="button">6</button>
                        </div>
                        <div id="keypad_row">
                            <button id="keypad_7" class="keypad-number-button" digit="7" type="button">7</button>
                            <button id="keypad_8" class="keypad-number-button" digit="8" type="button">8</button>
                            <button id="keypad_9" class="keypad-number-button" digit="9" type="button">9</button>
                        </div>
                        <div id="keypad_row">
                            <button id="keypad_delete" class="keypad-number-button" type="button">SİL</button>
                            <button id="keypad_0" class="keypad-number-button" digit="0" type="button">0</button>
                            <button id="keypad_decimal_point" class="keypad-number-button" type="button">&nbsp;</button>
                        </div>
                        <button id="keypad_submit">GÖNDER</button>
                    </div>
                </div>
            </div>
            <div id="container-column">
                <div id="information">
                    <div id="information-header">
                        <img id="logo" src="~/Content/Tablet/img/logo.png" />
                        <div id="information-details">
                            <span id="referee">Hakem : <span property-name="referee_name"></span></span>
                            <span id="athlete">Sporcu : <span property-name="athlete_name"></span></span>
                        </div>
                    </div>
                    <div id="row-prototype">
                        <div id="row">
                            <div id="athlete-order">1</div>
                            <div id="athlete-name">Doğan Çoruh</div>
                            <div class="score" id="score-1">44.2</div>
                            <div class="score" id="score-2">32.6</div>
                            <div class="score" id="score-3">55.8</div>
                            <div class="score" id="best-trick-1">44.2</div>
                            <div class="score" id="best-trick-2">32.6</div>
                            <div class="score" id="best-trick-3">55.8</div>
                            <div class="score" id="best-trick-4">32.6</div>
                            <div class="score" id="best-trick-5">55.8</div>
                        </div>
                    </div>
                    <div id="list-header">
                        <div id="athlete-order">#</div>
                        <div id="athlete-name">Ad Soyad</div>
                        <div class="score" id="score-1-title">S 1</div>
                        <div class="score" id="score-2-title">S 2</div>
                        <div class="score" id="score-3-title">S 3</div>
                        <div class="score" id="best-trick-1-title">BT 1</div>
                        <div class="score" id="best-trick-2-title">BT 2</div>
                        <div class="score" id="best-trick-3-title">BT 3</div>
                        <div class="score" id="best-trick-4-title">BT 4</div>
                        <div class="score" id="best-trick-5-title">BT 5</div>
                    </div>
                    <div id="information-athlete-list"></div>
                </div>
            </div>
        </div>
    </viewstate>

    <viewstate id="timing">
        <img id="background" src="@Url.Content("~/Content/Tablet/img/tablet_screen1.jpg")" />
        <div id="container">
            <div id="title">
                <img id="logo" src="~/Content/Tablet/img/logo.png" />
                <span id="text">TÜRKİYE KAYKAY FEDERASYONU</span>
            </div>
            <div id="information">
                <span property-name="tournament_name"></span>
                <span id="referee">Hakem : <span property-name="referee_name"></span></span>
                <span id="athlete">Sporcu : <span property-name="athlete_name"></span></span>
            </div>
            <button id="buttonStart">BAŞLAT</button>
        </div>
    </viewstate>

    <script>
        @if (!string.IsNullOrWhiteSpace(Model.IPAddress))
        {
            <text>
                window.ipAddress = '@Model.IPAddress';
            </text>
        }
    </script>
</body>

</html>