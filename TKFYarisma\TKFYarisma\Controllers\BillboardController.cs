﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Mvc;
using TKFYarisma.Filters;
using TKFYarisma.Helpers;
using TKFYarisma.Models;

namespace TKFYarisma.Controllers
{
    public class BillboardController : Controller
    {
        public ActionResult Index()
        {
            return Content("working");
        }

        public ActionResult Interface()
        {
            return View();
        }

        [AllowCrossSite]
        public ActionResult Data()
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                var billboard = TKFBillboardHelper.GetBillboard(dbContext);
                if (billboard.ViewState == "default")
                    return TKFBillboardHelper.GetViewStateDefault(dbContext);
                else if (billboard.ViewState == "athlete_orders")
                    return TKFBillboardHelper.GetViewStateAthleteOrders(dbContext);
                else if (billboard.ViewState == "athlete_info")
                    return TKFBillboardHelper.GetViewStateAthleteInformation(dbContext);
                else if (billboard.ViewState == "athlete_timer")
                    return TKFBillboardHelper.GetViewStateAthleteTimer(dbContext);
                else if (billboard.ViewState == "athlete_score")
                    return TKFBillboardHelper.GetViewStateAthleteScore(dbContext);
                else if (billboard.ViewState == "athlete_scores")
                    return TKFBillboardHelper.GetViewStateAthleteScores(dbContext);
                else if (billboard.ViewState == "medal_ceromony")
                    return TKFBillboardHelper.GetViewStateMedalCeromony(dbContext);
                else if (billboard.ViewState == "video_player")
                    return TKFBillboardHelper.GetViewStateVideoPlayer(dbContext);
                else if (billboard.ViewState == "referees")
                    return TKFBillboardHelper.GetViewStateReferees(dbContext);
                else
                    return Content("invalid_view_state_name");
            }
        }

        [HttpPost]
        public ActionResult TimerFinished()
        {
            using (var dbContext = new TKFYarismaEntities())
            {
                var billboard = (from table in dbContext.Billboards select table).FirstOrDefault();
                if (billboard != null)
                {
                    billboard.TimerFinished = true;
                    dbContext.SaveChanges();

                    return Content("success");
                }
            }

            return Content("failed");
        }
    }
}