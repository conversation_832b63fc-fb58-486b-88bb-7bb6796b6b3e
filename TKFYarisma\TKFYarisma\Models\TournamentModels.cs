﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace TKFYarisma.Models
{
    public class TournamentGroupsModel
    {
        public Tournament Tournament { get; set; }
        public IEnumerable<TournamentGroup> TournamentGroups { get; set; }
    }

    public class TournamentGroupModel
    {
        public Tournament Tournament { get; set; }
        public TournamentGroup TournamentGroup { get; set; }
    }

    public class TournamentGroupRefereesModel
    {
        public Tournament Tournament { get; set; }
        public TournamentGroup TournamentGroup { get; set; }
        public IEnumerable<Referee> Referees { get; set; }

        public int ChiefRefereeId { get; set; }
        public int SecretaryRefereeId { get; set; }
        public int TimekeeperId { get; set; }
        public int ScoreReferee1Id { get; set; }
        public int ScoreReferee2Id { get; set; }
        public int ScoreReferee3Id { get; set; }
        public int ScoreReferee4Id { get; set; }
        public int ScoreReferee5Id { get; set; }
    }

    public class TournamentReglamentModel
    {
        public Tournament Tournament { get; set; }    
    }

    public class TournamentGroupAthletesModel
    {
        public Tournament Tournament { get; set; }
        public TournamentGroup TournamentGroup { get; set; }
        public IEnumerable<ViewTournamentGroupAthlete> TournamentGroupAthletes { get; set; }


    }
}