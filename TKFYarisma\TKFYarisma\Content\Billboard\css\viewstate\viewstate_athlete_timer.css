#athlete_timer #background {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
}

#athlete_timer #tournament {
    position: absolute;
    left: 500px;
    top: 140px;
    width: 1420px;
    height: 60px;
}

    #athlete_timer #tournament #background {
        position: absolute;
        right: 0px;
        top: 0px;
        width: auto;
        height: auto;
    }

    #athlete_timer #tournament #tournament_name_and_phase {
        color: #f0f0f0;
        position: absolute;
        padding-right: 0px;
        right: 70px;
        top: 10px;
        width: 1250px;
        height: 350px;
        font-size: 30pt;
        text-align: right;
        line-height: 30pt;
        letter-spacing: 2px;
        text-shadow: 0px 0px 5px #800000;
    }

#athlete_timer #athlete {
    position: absolute;
    left: 300px;
    top: 120px;
    width: 1620px;
    height: 600px;
}

    #athlete_timer #athlete #background {
        position: absolute;
        left: 0px;
        top: 140px;
        width: auto;
        height: auto;
    }

#athlete_timer #photo {
    position: absolute;
    left: 200px;
    top: 185px;
    width: 675px;
    height: 675px;
    object-fit: contain;
    -webkit-mask-image: url(../../img/photo_mask.png);
    mask-image: url(../img/photo_mask.png);
    -webkit-mask-position: center center;
    mask-position: center center;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
}

#athlete_timer #helmet {
    position: absolute;
    left: 650px;
    top: 685px;
    width: 150px;
    height: 150px;
    object-fit: contain;
}

#athlete_timer #athlete #name {
    color: #f0f0f0;
    position: absolute;
    left: 600px;
    top: 190px;
    width: 1050px;
    height: auto;
    font-size: 80pt;
    line-height: 80pt;
    text-shadow: 2px 2px 5px #800000;
}

#athlete_timer #athlete #district_and_city {
    color: #f0f0f0;
    position: absolute;
    left: 600px;
    top: 310px;
    width: 1050px;
    height: auto;
    font-size: 40pt;
    line-height: 40pt;
    text-shadow: 2px 2px 5px #800000;
}

#athlete_timer #timer {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    left: 900px;
    top: 570px;
    width: 900px;
    height: 250px;
}

    #athlete_timer #timer .digit {
        display: flex;
        justify-content: center;
        align-items: center;
        float: left;
        width: 138px;
        height: 186px;
        background: url("../../img/viewstate/viewstate_athlete_timer/timer_background.png");
        background-size: 138px 186px;
        background-repeat: no-repeat;
        font-family: "MyriadPro-Bold";
        font-weight: bold;
        font-size: 110pt;
    }

    #athlete_timer #timer .digit-seperator {
        display: flex;
        justify-content: center;
        align-items: center;
        float: left;
        width: 50px;
        height: 186px;
        font-size: 80pt;
    }

.digit-gap {
    margin-left: 10px;
}

#athlete_timer #previous_score {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    left: 1030px;
    top: 850px;
    width: 625px;
    height: 150px;
}

    #athlete_timer #previous_score #background {
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        left: 0px;
        top: 0px;
        width: 625px;
        height: 150px;
    }

    #athlete_timer #previous_score #text {
        position: absolute;
        left: 50px;
        top: 50px;
        font-family: "Exo-BlackItalic";
        font-size: 50pt;
        font-weight: bold;
        color: #3a3a3a;
        /* color: #f1f1f1; */
    }

    #athlete_timer #previous_score #value {
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        left: 250px;
        top: -2px;
        font-family: "Exo-BlackItalic";
        font-size: 90pt;
        font-weight: bold;
        color: #3a3a3a;
    }