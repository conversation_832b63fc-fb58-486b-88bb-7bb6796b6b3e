﻿@using TKFYarisma.Helpers

@model TKFYarisma.Models.TournamentGroupAthletesModel

@{
    ViewBag.Title = "Grup Sporcuları";
    ViewBag.Section = "TournamentGroupAthlete";
}

<div class="row">
    <div class="col">
        <div class="tile-header tile-header-big">
            <div class="row">
                <div class="col mr-auto">
                    <h3>Grup Sporcuları (@Model.TournamentGroup.Name)</h3>
                </div>
                <div class="col-auto text-right">
                    <div class="dropdown">
                        <button class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="icon fas fa-plus"></i>Sporcu Ekle
                        </button>
                        <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton">
                            <a href="@Url.Action("AddTournamentGroupAthletes")" class="dropdown-item"><i class="icon fas fa-plus"></i>Havuzdan Sporcu Ekle</a>
                            <a href="@Url.Action("CreateAthlete")" class="dropdown-item"><i class="icon fas fa-plus"></i>Yeni Sporcu Ekle</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tile">
            <div class="row">
                <div class="col">
                    @if (Model.TournamentGroupAthletes.Count() > 0)
                    {
                        var grid = new WebGrid(Model.TournamentGroupAthletes,
                                                canPage: true,
                                                rowsPerPage: 100,
                                                selectionFieldName: "selectedRow",
                                                ajaxUpdateContainerId: "gridContent");

                        var gridColumns = new List<WebGridColumn>();

                        gridColumns.Add(grid.Column("LicenseNumber", "Lisans No.", style: "table-icon-column"));
                        gridColumns.Add(grid.Column("FirstName", "Adı", style: "table-icon-column"));
                        gridColumns.Add(grid.Column("LastName", "Soyadı", style: "table-icon-column"));
                        gridColumns.Add(grid.Column("Gender", header: "Cinsiyet", format: (item) =>
                        {
                            if (item.Gender != null)
                            {
                                if (item.Gender == 1)
                                    return Html.Raw("Erkek");
                                else if (item.Gender == 2)
                                    return Html.Raw("Kadın");
                                else
                                    return Html.Raw("---");
                            }
                            else
                                return Html.Raw("---");
                        }, style: "table-column"));
                        
                        
                        gridColumns.Add(grid.Column("Id", " ", format: (item) => { return Html.IconLink("RemoveTournamentGroupAthlete", new { id = item.Id }, IconType.Delete, IconSize.Large, IconColor.Black, "Sil"); }, style: "table-icon-column"));

                        grid.Pager(WebGridPagerModes.All);

                        <div id="gridContent">
                            @grid.GetHtml(
                                        tableStyle: "table table-hover table-bordered",
                                        headerStyle: "sorting_asc",
                                        footerStyle: "table-footer",
                                        rowStyle: "even",
                                        alternatingRowStyle: "odd",
                                        selectedRowStyle: "webgrid-selected-row",
                                        columns: gridColumns)
                        </div>
                    }
                    else
                    {
                        <div class="no-record">
                            Henüz hiçbir sporcu kaydı bulunmamaktadır.
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
