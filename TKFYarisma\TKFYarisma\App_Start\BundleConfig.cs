﻿using System.Web;
using System.Web.Optimization;

namespace TKFYarisma
{
    public class BundleConfig
    {
        // For more information on bundling, visit https://go.microsoft.com/fwlink/?LinkId=301862
        public static void RegisterBundles(BundleCollection bundles)
        {
            bundles.Add(new ScriptBundle("~/bundles/jquery").Include(
                        "~/Content/Site/scripts/jquery-3.3.1.min.js"));

            bundles.Add(new ScriptBundle("~/bundles/jqueryval").Include(
                        "~/Content/Site/scripts/jquery.validate*"));

            // Use the development version of Modernizr to develop with and learn from. Then, when you're
            // ready for production, use the build tool at https://modernizr.com to pick only the tests you need.
            bundles.Add(new ScriptBundle("~/bundles/modernizr").Include(
                        "~/Content/Site/scripts/modernizr-*"));

            bundles.Add(new ScriptBundle("~/bundles/bootstrap").Include(
                      "~/Content/Site/scripts/popper.min.js",
                      "~/Content/Site/scripts/bootstrap.min.js",
                      "~/Content/Site/scripts/moment-with-locales.min.js",
                      "~/Content/Site/scripts/bootstrap-datetimepicker.min.js"));

            bundles.Add(new StyleBundle("~/Content/css").Include(
                      "~/Content/Site/styles/bootstrap.min.css",
                      "~/Content/Site/styles/bootstrap-datetimepicker.min.css",
                      "~/Content/Site/styles/bootstrap-helper.css",
                      "~/Content/Site/styles/fontawesome-all.min.css",
                      "~/Content/Site/styles/table.css",
                      "~/Content/Site/styles/webgrid.css"));

            bundles.Add(new StyleBundle("~/Content/site").Include(
                      "~/Content/Site/styles/site.css"));
        }
    }
}
