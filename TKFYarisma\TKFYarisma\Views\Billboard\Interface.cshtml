﻿
@{
    Layout = null;
}

<!DOCTYPE html>
<html lang="tr">

<head>
    <meta charset="utf-8" />

    <script src="@Url.Content("~/Content/Billboard/js/libraries/jquery-3.3.1.min.js")"></script>
    <script src="@Url.Content("~/Content/Billboard/js/preferences.js")"></script>
    <script src="@Url.Content("~/Content/Billboard/js/viewstate/core/viewstate_helper.js")"></script>
    <script src="@Url.Content("~/Content/Billboard/js/viewstate/core/element_helper.js")"></script>
    <script src="@Url.Content("~/Content/Billboard/js/viewstate/viewstate.js")"></script>
    <script src="@Url.Content("~/Content/Billboard/js/viewstate/controllers/viewstate_default_controller.js")"></script>
    <script src="@Url.Content("~/Content/Billboard/js/viewstate/controllers/viewstate_athlete_orders_controller.js")"></script>
    <script src="@Url.Content("~/Content/Billboard/js/viewstate/controllers/viewstate_athlete_info_controller.js")"></script>
    <script src="@Url.Content("~/Content/Billboard/js/viewstate/controllers/viewstate_athlete_timer_controller.js")"></script>
    <script src="@Url.Content("~/Content/Billboard/js/viewstate/controllers/viewstate_athlete_score_controller.js")"></script>
    <script src="@Url.Content("~/Content/Billboard/js/viewstate/controllers/viewstate_athlete_scores_controller.js")"></script>
    <script src="@Url.Content("~/Content/Billboard/js/viewstate/controllers/viewstate_medal_ceromony_controller.js")"></script>
    <script src="@Url.Content("~/Content/Billboard/js/viewstate/controllers/viewstate_video_player_controller.js")"></script>
    <script src="@Url.Content("~/Content/Billboard/js/viewstate/controllers/viewstate_referees_controller.js")"></script>
    <script src="@Url.Content("~/Content/Billboard/js/viewstate/core/viewstate_main.js")"></script>

    <link rel="stylesheet" href="@Url.Content("~/Content/Billboard/css/animate.css")">
    <link rel="stylesheet" href="@Url.Content("~/Content/Billboard/css/styles.css")">
    <link rel="stylesheet" href="@Url.Content("~/Content/Billboard/css/viewstate/viewstate_default.css")">
    <link rel="stylesheet" href="@Url.Content("~/Content/Billboard/css/viewstate/viewstate_athlete_orders.css")">
    <link rel="stylesheet" href="@Url.Content("~/Content/Billboard/css/viewstate/viewstate_athlete_info.css")">
    <link rel="stylesheet" href="@Url.Content("~/Content/Billboard/css/viewstate/viewstate_athlete_timer.css")">
    <link rel="stylesheet" href="@Url.Content("~/Content/Billboard/css/viewstate/viewstate_athlete_score.css")">
    <link rel="stylesheet" href="@Url.Content("~/Content/Billboard/css/viewstate/viewstate_athlete_scores.css")">
    <link rel="stylesheet" href="@Url.Content("~/Content/Billboard/css/viewstate/viewstate_medal_ceromony.css")">
    <link rel="stylesheet" href="@Url.Content("~/Content/Billboard/css/viewstate/viewstate_video_player.css")">
    <link rel="stylesheet" href="@Url.Content("~/Content/Billboard/css/viewstate/viewstate_referees.css")">
</head>

<body>
    <viewstate id="default">
        <video id="video" src="@Url.Content("~/Content/Billboard/video/tkf_intro.mp4")" autoplay="autoplay" loop="loop"></video>
        <img id="background" src="@Url.Content("~/Content/Billboard/img/tkf_intro.png")" />
    </viewstate>

    <viewstate id="athlete_orders">
        <img id="background" src="@Url.Content("~/Content/Billboard/img/background_athlete_info_green.jpg")" />
        <div id="tournament">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/tourtnament_name_stripe_green.png")" />
            <span id="tournament_name_and_phase" property-name="tournament_name_and_phase"></span>
            <span id="phase_name" property-name="phase_name"></span>
        </div>
        <div id="list">
            <div id="row" in-transition-class="fadeInRight" out-transition-class="fadeOutLeft">
                <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_athlete_orders/scores_row_background.png")" />
                <div id="container">
                    <img id="athlete_order_background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_athlete_orders/order_background.png")" />
                    <span id="athlete_order">-</span>
                    <div id="athlete_and_city_name_holder">
                        <span id="athlete_name">---</span>
                        <span id="athlete_city_name">---</span>
                    </div>
                </div>
            </div>
            <div id="row_container">
                <div id="scroller"></div>
            </div>
        </div>
    </viewstate>

    <viewstate id="athlete_info">
        <img id="background" src="@Url.Content("~/Content/Billboard/img/background_athlete_info_green.jpg")" />
        <div id="tournament">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/tourtnament_name_stripe_green.png")" />
            <span id="tournament_name_and_phase" property-name="tournament_name_and_phase"></span>
            <span id="phase_name" property-name="phase_name"></span>
        </div>
        <div id="athlete" in-transition-class="fadeInRight" out-transition-class="fadeOutRight">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/athlete_name_stripe_green.png")" />
            <span id="name" property-name="athlete_name"></span>
            <span id="district_and_city" property-name="athlete_district_and_city"></span>
        </div>
        <img id="photo" property-name="athlete_photo" in-transition-class="fadeInLeft" out-transition-class="fadeOutLeft" />
        <img id="helmet" src="@Url.Content("~/Content/Billboard/img/helmet.png")" in-transition-class="fadeInLeft" out-transition-class="fadeOutLeft" />
    </viewstate>

    <viewstate id="athlete_timer">
        <img id="background" src="@Url.Content("~/Content/Billboard/img/background_athlete_info_green.jpg")" />
        <div id="tournament">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/tourtnament_name_stripe_green.png")" />
            <span id="tournament_name_and_phase" property-name="tournament_name_and_phase"></span>
            <span id="phase_name" property-name="phase_name"></span>
        </div>
        <div id="athlete" in-transition-class="fadeInRight" out-transition-class="fadeOutLeft">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/athlete_name_stripe_green.png")" />
            <span id="name" property-name="athlete_name"></span>
            <span id="district_and_city" property-name="athlete_district_and_city"></span>
        </div>
        <div id="timer" in-transition-class="fadeInUp" out-transition-class="fadeOutDown">
            <span class="digit" property-name="timer_digit_1">0</span>
            <span class="digit digit-gap" property-name="timer_digit_2">0</span>
            <div class="digit-seperator digit-gap">:</div>
            <span class="digit digit-gap" property-name="timer_digit_3">0</span>
            <span class="digit digit-gap" property-name="timer_digit_4">0</span>
        </div>
        <img id="photo" property-name="athlete_photo" />
        <img id="helmet" src="@Url.Content("~/Content/Billboard/img/helmet.png")" in-transition-class="fadeInLeft" out-transition-class="fadeOutLeft" />
        <div id="previous_score" in-transition-class="fadeInRight" out-transition-class="fadeOutLeft">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_athlete_score/athlete_score_background.png")" />
            <span id="text">1.Tur</span>
            <span id="value" property-name="previous_score"></span>
        </div>
    </viewstate>

    <viewstate id="athlete_score">
        <img id="background" src="@Url.Content("~/Content/Billboard/img/background_athlete_info_green.jpg")" />
        <div id="tournament">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/tourtnament_name_stripe_green.png")" />
            <span id="tournament_name_and_phase" property-name="tournament_name_and_phase"></span>
            <span id="phase_name" property-name="phase_name"></span>
        </div>
        <div id="athlete" in-transition-class="fadeInRight" out-transition-class="fadeOutLeft">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/athlete_name_stripe_green.png")" />
            <span id="name" property-name="athlete_name"></span>
            <span id="district_and_city" property-name="athlete_district_and_city"></span>
        </div>
        <div id="order" in-transition-class="fadeInRight" out-transition-class="fadeOutLeft">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_athlete_score/athlete_order_background.png")" />
            <span id="value" property-name="order"></span>
        </div>
        <div id="score_1" in-transition-class="fadeInRight" out-transition-class="fadeOutLeft">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_athlete_score/athlete_score_background.png")" />
            <span id="text">1.Tur</span>
            <span id="value" property-name="score_1"></span>
        </div>
        <div id="score_2" in-transition-class="fadeInRight" out-transition-class="fadeOutLeft">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_athlete_score/athlete_score_background.png")" />
            <span id="text">2.Tur</span>
            <span id="value" property-name="score_2"></span>
        </div>
        <div id="score_3" in-transition-class="fadeInRight" out-transition-class="fadeOutLeft">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_athlete_score/athlete_score_background.png")" />
            <span id="text">3.Tur</span>
            <span id="value" property-name="score_3"></span>
        </div>

        <div id="score_1b" in-transition-class="fadeInRight" out-transition-class="fadeOutLeft">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_athlete_score/athlete_score_small_background.png")" />
            <span id="text">1.Tur</span>
            <span id="value" property-name="score_1"></span>
        </div>
        <div id="score_2b" in-transition-class="fadeInRight" out-transition-class="fadeOutLeft">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_athlete_score/athlete_score_small_background.png")" />
            <span id="text">2.Tur</span>
            <span id="value" property-name="score_2"></span>
        </div>
        <div id="score_3b" in-transition-class="fadeInRight" out-transition-class="fadeOutLeft">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_athlete_score/athlete_score_small_background.png")" />
            <span id="text">3.Tur</span>
            <span id="value" property-name="score_3"></span>
        </div>

        <div id="score_best_trick_1" in-transition-class="fadeInRight" out-transition-class="fadeOutLeft">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_athlete_score/athlete_score_small_background.png")" />
            <span id="text">BT.1</span>
            <span id="value" property-name="best_trick_1"></span>
        </div>
        <div id="score_best_trick_2" in-transition-class="fadeInRight" out-transition-class="fadeOutLeft">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_athlete_score/athlete_score_small_background.png")" />
            <span id="text">BT.2</span>
            <span id="value" property-name="best_trick_2"></span>
        </div>
        <div id="score_best_trick_3" in-transition-class="fadeInRight" out-transition-class="fadeOutLeft">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_athlete_score/athlete_score_small_background.png")" />
            <span id="text">BT.3</span>
            <span id="value" property-name="best_trick_3"></span>
        </div>
        <div id="score_best_trick_4" in-transition-class="fadeInRight" out-transition-class="fadeOutLeft">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_athlete_score/athlete_score_small_background.png")" />
            <span id="text">BT.4</span>
            <span id="value" property-name="best_trick_4"></span>
        </div>
        <div id="score_best_trick_5" in-transition-class="fadeInRight" out-transition-class="fadeOutLeft">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_athlete_score/athlete_score_small_background.png")" />
            <span id="text">BT.5</span>
            <span id="value" property-name="best_trick_5"></span>
        </div>

        <img id="photo" property-name="athlete_photo" />
        <img id="helmet" src="@Url.Content("~/Content/Billboard/img/helmet.png")" in-transition-class="fadeInLeft" out-transition-class="fadeOutLeft" />
    </viewstate>

    <viewstate id="athlete_scores">
        <img id="background" src="@Url.Content("~/Content/Billboard/img/background_athlete_info_green.jpg")" />
        <div id="tournament">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/tourtnament_name_stripe_green.png")" />
            <span id="tournament_name_and_phase" property-name="tournament_name_and_phase"></span>
            <span id="phase_name" property-name="phase_name"></span>
        </div>
        <div id="list">
            <div id="title-container">
                <span id="score_1_title" in-transition-class="fadeInRight" out-transition-class="fadeOutLeft">1.TUR</span>
                <span id="score_2_title" in-transition-class="fadeInRight" out-transition-class="fadeOutLeft">2.TUR</span>
                <span id="score_3_title" in-transition-class="fadeInRight" out-transition-class="fadeOutLeft">3.TUR</span>
            </div>
            <div id="row-prototype">
                <div id="row" in-transition-class="fadeInRight" out-transition-class="fadeOutLeft">
                    <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_athlete_scores/scores_row_background.png")" />
                    <div id="container">
                        <div id="athlete_order">
                            <img id="athlete_order_background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_athlete_scores/order_background.png")" />
                            <span id="athlete_order_text">1</span>
                        </div>
                        <div id="athlete_and_city_name_holder">
                            <span id="athlete_name">Adem USTAOĞLU</span>
                            <span id="athlete_city_name">---</span>
                        </div>
                        <div id="score_1">
                            <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_athlete_scores/score_background.png")" />
                            <span id="value">-</span>
                        </div>
                        <div id="score_2">
                            <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_athlete_scores/score_background.png")" />
                            <span id="value">-</span>
                        </div>
                        <div id="score_3">
                            <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_athlete_scores/score_background.png")" />
                            <span id="value">-</span>
                        </div>
                        <span id="queue">Q</span>
                    </div>
                </div>
            </div>
            <div id="row_container">
                <div id="scroller"></div>
            </div>
        </div>
    </viewstate>
    <viewstate id="medal_ceromony">
        <img id="background" src="@Url.Content("~/Content/Billboard/img/background_athlete_info_green.jpg")" />
        <div id="tournament">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/tourtnament_name_stripe_green.png")" />
            <span id="tournament_name" property-name="tournament_name"></span>
            <span id="phase_name" property-name="phase_name"></span>
        </div>
        <div id="gold_medal" in-transition-class="fadeInLeft" out-transition-class="fadeOutLeft">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_medal_ceromony/photo_canvas_gold.png")" />
            <img id="athlete_photo" property-name="gold_medal/athlete_photo" />
            <span id="athlete_name" property-name="gold_medal/athlete_name"></span>
        </div>
        <div id="gold_medal_order" in-transition-class="fadeInDown" out-transition-class="fadeOutDown">1</div>

        <div id="silver_medal" in-transition-class="fadeInLeft" out-transition-class="fadeOutLeft">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_medal_ceromony/photo_canvas_silver.png")" />
            <img id="athlete_photo" property-name="silver_medal/athlete_photo" />
            <span id="athlete_name" property-name="silver_medal/athlete_name"></span>
        </div>
        <div id="silver_medal_order" in-transition-class="fadeInDown" out-transition-class="fadeOutDown">2</div>

        <div id="bronze_medal" in-transition-class="fadeInLeft" out-transition-class="fadeOutLeft">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_medal_ceromony/photo_canvas_bronze.png")" />
            <img id="athlete_photo" property-name="bronze_medal/athlete_photo" />
            <span id="athlete_name" property-name="bronze_medal/athlete_name"></span>
        </div>
        <div id="bronze_medal_order" in-transition-class="fadeInDown" out-transition-class="fadeOutDown">3</div>
    </viewstate>
    <viewstate id="referees">
        <img id="viewstate_background" src="@Url.Content("~/Content/Billboard/img/background_athlete_info_green.jpg")" />
        <div id="tournament">
            <img id="background" src="@Url.Content("~/Content/Billboard/img/tourtnament_name_stripe_green.png")" />
            <span id="tournament_name" property-name="tournament_name"></span>
            <span id="phase_name" property-name="phase_name"></span>
        </div>
        <div id="row1">
            <div id="president" in-transition-class="fadeInDown" out-transition-class="fadeOutDown">
                <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_referees/photo_canvas_referee.png")" />
                <span id="type"></span>
                <img id="photo" />
                <span id="name"></span>
            </div>
            <div id="chairperson" in-transition-class="fadeInDown" out-transition-class="fadeOutDown">
                <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_referees/photo_canvas_referee.png")" />
                <span id="type"></span>
                <img id="photo" />
                <span id="name"></span>
            </div>
            <div id="observer" in-transition-class="fadeInDown" out-transition-class="fadeOutDown">
                <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_referees/photo_canvas_referee.png")" />
                <span id="type"></span>
                <img id="photo" />
                <span id="name"></span>
            </div>
            <div id="chief_referee" in-transition-class="fadeInDown" out-transition-class="fadeOutDown">
                <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_referees/photo_canvas_referee.png")" />
                <span id="type"></span>
                <img id="photo" />
                <span id="name"></span>
            </div>
            <div id="secretary_referee" in-transition-class="fadeInDown" out-transition-class="fadeOutDown">
                <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_referees/photo_canvas_referee.png")" />
                <span id="type"></span>
                <img id="photo" />
                <span id="name"></span>
            </div>
            <div id="timing_referee" in-transition-class="fadeInDown" out-transition-class="fadeOutDown">
                <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_referees/photo_canvas_referee.png")" />
                <span id="type"></span>
                <img id="photo" />
                <span id="name"></span>
            </div>
        </div>
        <div id="row2">
            <div id="scoring_referee1" in-transition-class="fadeInLeft" out-transition-class="fadeOutLeft">
                <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_referees/photo_canvas_referee.png")" />
                <span id="type"></span>
                <img id="photo" />
                <span id="name"></span>
            </div>
            <div id="scoring_referee2" in-transition-class="fadeInLeft" out-transition-class="fadeOutLeft">
                <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_referees/photo_canvas_referee.png")" />
                <span id="type"></span>
                <img id="photo" />
                <span id="name"></span>
            </div>
            <div id="scoring_referee3" in-transition-class="fadeInLeft" out-transition-class="fadeOutLeft">
                <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_referees/photo_canvas_referee.png")" />
                <span id="type"></span>
                <img id="photo" />
                <span id="name"></span>
            </div>
            <div id="scoring_referee4" in-transition-class="fadeInLeft" out-transition-class="fadeOutLeft">
                <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_referees/photo_canvas_referee.png")" />
                <span id="type"></span>
                <img id="photo" />
                <span id="name"></span>
            </div>
            <div id="scoring_referee5" in-transition-class="fadeInLeft" out-transition-class="fadeOutLeft">
                <img id="background" src="@Url.Content("~/Content/Billboard/img/viewstate/viewstate_referees/photo_canvas_referee.png")" />
                <span id="type"></span>
                <img id="photo" />
                <span id="name"></span>
            </div>
        </div>
    </viewstate>
    <viewstate id="video_player">
        <video id="video" autoplay="autoplay" loop="loop"></video>
    </viewstate>
</body>

</html>