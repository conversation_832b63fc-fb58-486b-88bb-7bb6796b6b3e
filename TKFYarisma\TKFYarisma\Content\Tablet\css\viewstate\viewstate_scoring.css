﻿#scoring {
    box-sizing: content-box;
    user-select: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: "Roboto Condensed", sans-serif;
    color: #f0F0F0;
    text-shadow: 2px 1px #000000;
}

    #scoring #background {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: -1;
    }

    #scoring #container {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
        flex-wrap: nowrap;
        left: auto;
        width: 920px;
        max-width: 920px;
        height: 550px;
        text-align: center;
        flex: 1;
        z-index: 1;
    }

    #scoring #keypad-column {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-direction: column;
    }

    #scoring #container-column {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        height: 100%;
        flex: 1;
    }

    #scoring #information {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        flex: 1;
        width: 100%;
        height: 100%;
        color: #000;
        text-shadow: none;
    }

        #scoring #information #row-prototype {
            display: none;
        }

    #scoring #container #title {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }

        #scoring #container #title #text {
            font-size: 36pt;
        }

    #scoring #information-header {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        height: 100px;
        margin-left: 10px;
        margin-bottom: 20px;
    }

    #scoring #logo {
        width: 40%;
        display: block;
    }

    #scoring #information-details {
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        flex-direction: column;
        font-size: 24pt;
        flex: 1;
        margin-left: 20px;
    }

    #scoring #container #information #referee {
        font-size: 14pt;
    }

    #scoring #container #information #athlete {
        font-size: 14pt;
        margin-top: 5px;
    }

    #scoring #information-athlete-list {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        background-color: rgba(255, 255, 255, 0.2);
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
        height: 100%;
        overflow-y: scroll;
    }

    #scoring #list-header {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        width: 580px;
        min-height: 40px;
        background-color: #dedede;
        font-family: Arial;
        font-size: 12pt;
        text-shadow: none;
        color: #000;
        padding-right: 10px;
        margin-bottom: 2px;
        font-weight: bold;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
    }

        #scoring #list-header #athlete-order {
            width: 70px;
        }

        #scoring #list-header #athlete-name {
            flex: 1;
        }

        #scoring #list-header .score {
            width: 50px;
        }

    #scoring #row {
        width: 580px;
        height: 40px;
        min-height: 40px;
        margin-top: 2px;
        margin-bottom: 2px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-family: Arial;
        font-size: 12pt;
        text-shadow: none;
        color: #000;
        padding-right: 10px;
    }

        #scoring #row:nth-child(even) {
            background-color: #efefef;
        }

        #scoring #row:nth-child(odd) {
            background-color: #e7e7e7;
        }

        #scoring #row #athlete-order {
            width: 70px;
        }

        #scoring #row #athlete-name {
            flex: 1;
        }

        #scoring #row .score {
            width: 50px;
        }

    #scoring #container #keypad {
        margin-left: 20px;
        margin-right: 20px;
        width: 260px;
        height: auto;
    }

        #scoring #container #keypad #display-container {
            font-family: arial;
            font-weight: bold;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 80px;
            margin: 2px;
            background-color: #efefef;
            font-size: 36pt;
            color: #0f0f0f;
            border: none;
            border-radius: 5px;
            margin-bottom: 10px;
        }

        #scoring #container #keypad .keypad-number-button {
            text-align: center;
            width: 80px;
            height: 80px;
            margin: 2px;
            background-color: #6c757d;
            font-size: 24pt;
            color: #fefefe;
            border: none;
            border-radius: 5px;
            padding: 0px;
        }

        #scoring #container #keypad #keypad_submit {
            width: 100%;
            height: 80px;
            margin: 2px;
            margin-top: 10px;
            background-color: #28a745;
            font-size: 24pt;
            color: #fefefe;
            border: none;
            border-radius: 5px;
            margin-left: 2px;
            margin-right: 2px;
            box-shadow: 0px 0px 15px #5a5a5a;
        }
