﻿body {
    font-family: 'Open Sans', sans-serif;
    font-size: 0.875rem;
    background-color: #e5e5e5;
}

.sign-in {
    background-color: #e5e5e5;
    color: #777777;
}

.sign-in-container {
    margin-top: 70px;
    width: 350px;
    max-width: 350px;
}

.wrapper {
    padding-top: 65px;
    display: flex;
    align-items: stretch;
    min-height: calc(100vh - 65px);
}

.navbar {
    background-color: #00635a !important;
}

.navbar-title {
    padding-top: 10px;
    font-size: 0.875rem;
    color: #efefef;
}

#sidebar {
    min-width: 250px;
    max-width: 250px;
    min-height: calc(100vh - 65px);
    background-color: #212529;
    color: #efefef;
}

#sidebar a {
    color: #efefef;
    text-decoration: none;
}

    #sidebar.active {
        margin-left: -250px;
    }

.divider {
    height: 5px;
}

.sidebar-menu {

}

.list-group-item {
    padding-top: 10px;
    padding-bottom: 10px;
    background-color: #212529;
    font-size: 0.875rem;
}

    .list-group-item.active {
        background-color: black;
        border: 1px solid black;
    }

.no-padding {
    padding: 0 !important;
    margin: 0 !important;
}

.logo {
    height: 40px;
    max-height: 40px;
}

.icon {
    margin-right: 10px;
}

.icon a {
    color: red !important;
}

.icon-white {
    color: #fefefe;
}

.icon-black {
    color: #0f0f0f;
}

.icon-and-text-container {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
}

.icon-gap {
    margin-right: 8px;
}

.tile-header {
    font-size: 1.7em;
    width: 100%;
    background-color: #f7f7f7;
    display: block;
    justify-content: flex-start;
    align-items: center;
    -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12), 0 3px 1px -2px rgba(0, 0, 0, 0.2);
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12), 0 3px 1px -2px rgba(0, 0, 0, 0.2);
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    -webkit-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}

.tile-header-small {
    height: 50px;
    padding-top: 8px;
    padding-left: 18px;
    padding-bottom: 20px;
}

.tile-header-big {
    height: 70px;
    padding-top: 17px;
    padding-left: 20px;
    padding-right: 20px;
    padding-bottom: 17px;
}

.tile {
    position: relative;
    background: #ffffff;
    border-bottom-left-radius: 3px;
    border-bottom-right-radius: 3px;
    padding-top: 20px;
    padding-left: 20px;
    padding-right: 20px;
    padding-bottom: 20px;
    -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12), 0 3px 1px -2px rgba(0, 0, 0, 0.2);
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12), 0 3px 1px -2px rgba(0, 0, 0, 0.2);
    margin-bottom: 30px;
    -webkit-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}

.dashboard-statistics {

}

.dashboard-tile {
    min-width: 250px;
    max-width: 400px;
}

.row-form {
    width: 500px;
    height: 50px;
    padding-left: 15px;
    padding-top: 5px;
    padding-bottom: 5px;
}

.row-form-dynamic {
    width: 500px;
    padding-left: 15px;
    padding-top: 5px;
    padding-bottom: 5px;
}

.row-image {
    width: 150px;
    height: 150px;
}

.row-form input[type=checkbox] {
    margin-top: 12px;
}

.row-form-footer {
    padding-left: 15px;
    padding-top: 10px;
}

.btn-primary {
    background-color: #00635a !important;
}

.col-form-label-small {
    width: 100px;
    display: flex;
    justify-content:flex-start;
    align-items: center;
}
.col-form-label-medium {
    width: 150px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}
.col-form-label-big {
    width: 200px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.breadcrumb {
    background-color: #c0c0c0;
}

    .breadcrumb a {
        text-decoration: none;
    }

.table-column {
    width: 32px;
    max-width: 32px;
}

.table-icon-column {

}

#reglament-toolbar .col-auto {
    padding-left: 5px;
    padding-right: 5px;
}