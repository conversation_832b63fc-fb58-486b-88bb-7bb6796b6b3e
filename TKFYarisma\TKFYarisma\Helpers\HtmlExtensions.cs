﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Web;
using System.Web.Mvc;
using System.Web.Mvc.Html;

namespace TKFYarisma.Helpers
{
    public static class HtmlExtensions
    {
        public static MvcHtmlString CheckBoxForEx<TModel>(this HtmlHelper<TModel> htmlHelper, Expression<Func<TModel, bool?>> expression, object htmlAttributes)
        {
            var expressionBody = (MemberExpression)expression.Body;
            return InputExtensions.CheckBox(htmlHelper, expressionBody.Member.Name, htmlAttributes);
        }
    }
}