function ViewStateDefaultController() {
    var self = this;
    var videoName = undefined;
    var $video = undefined;
    var timeoutId = undefined;

    this.viewStateName = "default";

    this.initialize = function (properties) {
        var $viewstate = ElementHelper.getElementById($("body"), self.viewStateName);

        clearTimeout(self.timeoutId);

        $video = $(ElementHelper.getElementById($viewstate, "video"));
        $video[0].play();
    }

    this.deinitialize = function () {
        self.timeoutId = setTimeout(self.stopVideo, 1000);
    }

    this.stopVideo = function () {
        self.timeoutId = undefined;
        $video[0].pause();
    }
}