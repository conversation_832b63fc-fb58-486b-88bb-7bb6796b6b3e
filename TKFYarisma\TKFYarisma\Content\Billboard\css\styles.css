@font-face {
    font-family: "<PERSON>";
    src: url('../font/Anton-Regular.ttf')  format('truetype')
}

@font-face {
    font-family: "Exo-BlackItalic";
    src: url('../font/Exo-BlackItalic.ttf')  format('truetype')
}

@font-face {
    font-family: "Exo-BoldItalic";
    src: url('../font/Exo-BoldItalic.ttf')  format('truetype')
}

@font-face {
    font-family: "FranklinGothic-MediumItalic";
    src: url('../font/FranklinGothic-MediumItalic.ttf')  format('truetype')
}

@font-face {
    font-family: "MyriadPro-Bold";
    src: url('../font/MyriadPro-Bold.otf')
}

body {
    font-family: "Anton", Tahoma, serif;
    background-color: #000;
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    /* user-select: none; */
    cursor: none;
}

viewstate {
    width: 100%;
    height: 100%;
    opacity: 0;
    pointer-events: none;
}

viewstate img {
    position: absolute;
    width: 100%;
    height: auto;
}

#commandBar {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: middle;
    position: absolute;
    right: 20px;
    bottom: 20px;
}

#commandBar button {    
    width: 100px;
    height: 40px;
}

.button-gap {
    margin-left: 5px;
}