﻿@{
    ViewBag.Title = "Preferences";
    ViewBag.Section = "Preferences";
}

<div class="row">
    <div class="col">
        <div class="tile-header tile-header-big">
            <div class="row">
                <div class="col mr-auto">
                    <h3>Preferences</h3>
                </div>
                <!--
                <div class="col-auto text-right">
                    <a href="@Url.Action("CreateProduct")" class="btn btn-dark"><i class="icon fas fa-plus"></i>Create Product</a>
                </div>
                -->
            </div>
        </div>
        <div class="tile">
            <div class="row">
                <div class="col">
                </div>
            </div>
        </div>
    </div>
</div>
